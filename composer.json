{"name": "impresscms/impresscms", "description": "ImpressCMS - A dynamic and user-friendly Content Management System", "type": "project", "keywords": ["cms", "content-management", "php", "mysql", "web"], "homepage": "https://www.impresscms.org/", "license": "GPL-2.0-or-later", "authors": [{"name": "The ImpressCMS Project", "homepage": "https://www.impresscms.org/", "role": "Developer"}], "require": {"php": ">=7.4.0", "ext-gd": "*", "ext-json": "*", "ext-mbstring": "*", "ext-mysqli": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-session": "*", "ext-xml": "*", "ext-zlib": "*"}, "require-dev": {"phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "^3.0"}, "autoload": {"psr-4": {"Icms\\": "htdocs/libraries/icms/"}, "psr-0": {"icms_": "htdocs/libraries/"}, "classmap": ["htdocs/libraries/icms.php"], "files": ["htdocs/include/functions.php"]}, "autoload-dev": {"psr-4": {"Icms\\Tests\\": "tests/"}}, "config": {"optimize-autoloader": true, "classmap-authoritative": false, "apcu-autoloader": true, "sort-packages": true, "allow-plugins": {"*": false}, "cache-dir": "var/cache/composer", "vendor-dir": "vendor"}, "scripts": {"test": "phpunit", "cs-check": "phpcs --standard=PSR12 htdocs/libraries/icms/", "cs-fix": "phpcbf --standard=PSR12 htdocs/libraries/icms/", "optimize-production": ["composer dump-autoload --optimize --classmap-authoritative --apcu", "@php -r \"echo 'Production autoloader optimized for ImpressCMS\\n';\""], "optimize-development": ["composer dump-autoload --optimize", "@php -r \"echo 'Development autoloader optimized for ImpressCMS\\n';\""], "post-autoload-dump": ["@php -r \"echo 'Autoloader optimized for ImpressCMS\\n';\""]}, "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "minimum-stability": "stable", "prefer-stable": true}