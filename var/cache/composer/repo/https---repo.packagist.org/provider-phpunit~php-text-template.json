{"minified": "composer/2.0", "packages": {"phpunit/php-text-template": [{"name": "phpunit/php-text-template", "description": "Simple template engine.", "keywords": ["template"], "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "version": "5.0.0", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "e1367a453f0eda562eedb4f659e13aa900d66c53"}, "dist": {"url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/e1367a453f0eda562eedb4f659e13aa900d66c53", "type": "zip", "shasum": "", "reference": "e1367a453f0eda562eedb4f659e13aa900d66c53"}, "type": "library", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:59:16+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "3e0404dc6b300e6bf56415467ebcb3fe4f33e964"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/3e0404dc6b300e6bf56415467ebcb3fe4f33e964", "type": "zip", "shasum": "", "reference": "3e0404dc6b300e6bf56415467ebcb3fe4f33e964"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/4.0.1"}, "time": "2024-07-03T05:08:43+00:00", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "d38f6cbff1cdb6f40b03c9811421561668cc133e"}, "dist": {"url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/d38f6cbff1cdb6f40b03c9811421561668cc133e", "type": "zip", "shasum": "", "reference": "d38f6cbff1cdb6f40b03c9811421561668cc133e"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/4.0.0"}, "time": "2024-02-02T06:06:56+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/0c7b06ff49e3d5072f057eb1fa59258bf287a748", "type": "zip", "shasum": "", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/3.0.1"}, "time": "2023-08-31T14:07:24+00:00", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "9f3d3709577a527025f55bcf0f7ab8052c8bb37d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/9f3d3709577a527025f55bcf0f7ab8052c8bb37d", "type": "zip", "shasum": "", "reference": "9f3d3709577a527025f55bcf0f7ab8052c8bb37d"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/3.0.0"}, "time": "2023-02-03T06:56:46+00:00"}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28"}, "dist": {"url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "type": "zip", "shasum": "", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/2.0.4"}, "time": "2020-10-26T05:33:50+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "18c887016e60e52477e54534956d7b47bc52cd84"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/18c887016e60e52477e54534956d7b47bc52cd84", "type": "zip", "shasum": "", "reference": "18c887016e60e52477e54534956d7b47bc52cd84"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/2.0.3"}, "time": "2020-09-28T06:03:05+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "6ff9c8ea4d3212b88fcf74e25e516e2c51c99324"}, "dist": {"url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/6ff9c8ea4d3212b88fcf74e25e516e2c51c99324", "type": "zip", "shasum": "", "reference": "6ff9c8ea4d3212b88fcf74e25e516e2c51c99324"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/master"}, "time": "2020-06-26T11:55:37+00:00", "require": {"php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "0c69cbf965d5317ba33f24a352539f354a25db09"}, "dist": {"url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/0c69cbf965d5317ba33f24a352539f354a25db09", "type": "zip", "shasum": "", "reference": "0c69cbf965d5317ba33f24a352539f354a25db09"}, "time": "2020-06-15T12:52:43+00:00", "require": {"php": "^7.3"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "526dc996cc0ebdfa428cd2dfccd79b7b53fee346"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/526dc996cc0ebdfa428cd2dfccd79b7b53fee346", "type": "zip", "shasum": "", "reference": "526dc996cc0ebdfa428cd2dfccd79b7b53fee346"}, "time": "2020-02-01T07:43:44+00:00", "funding": "__unset", "require-dev": "__unset"}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "type": "zip", "shasum": "", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.2.1"}, "time": "2015-06-21T13:50:34+00:00", "require": {"php": ">=5.3.3"}, "extra": "__unset"}, {"version": "1.2.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "206dfefc0ffe9cebf65c413e3d0e809c82fbf00a"}, "dist": {"url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/206dfefc0ffe9cebf65c413e3d0e809c82fbf00a", "type": "zip", "shasum": "", "reference": "206dfefc0ffe9cebf65c413e3d0e809c82fbf00a"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.2.0"}, "time": "2014-01-30T17:20:04+00:00", "autoload": {"classmap": ["Text/"]}, "include-path": [""]}, {"version": "1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "5180896f51c5b3648ac946b05f9ec02be78a0b23"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/5180896f51c5b3648ac946b05f9ec02be78a0b23", "type": "zip", "shasum": "", "reference": "5180896f51c5b3648ac946b05f9ec02be78a0b23"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.1.4"}, "time": "2012-10-31T18:15:28+00:00"}, {"homepage": "http://www.phpunit.de/", "version": "1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "6776b80c210af9ff69d79f34bb619f5f218b653d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/6776b80c210af9ff69d79f34bb619f5f218b653d", "type": "zip", "shasum": "", "reference": "6776b80c210af9ff69d79f34bb619f5f218b653d"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.1.3"}, "time": "2012-10-11T11:48:39+00:00"}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "type": "git", "reference": "1da672430d58dcbb21139a018febe038d8500fd8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/1da672430d58dcbb21139a018febe038d8500fd8", "type": "zip", "shasum": "", "reference": "1da672430d58dcbb21139a018febe038d8500fd8"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.1.2"}, "time": "2012-09-23T00:00:00+00:00", "autoload": {"files": ["Text/Template/Autoload.php"]}, "require": {"php": ">=5.2.7"}}]}, "security-advisories": [], "last-modified": "Fri, 07 Feb 2025 04:59:29 GMT"}