{"minified": "composer/2.0", "packages": {"webmozart/assert": [{"name": "webmozart/assert", "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["check", "assert", "validate"], "homepage": "", "version": "1.11.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "type": "zip", "shasum": "", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "type": "library", "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "funding": [], "time": "2022-06-03T18:03:27+00:00", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "require": {"php": "^7.2 || ^8.0", "ext-ctype": "*"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}}, {"version": "1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/6964c76c7804814a842473e0c8fd15bab0f18e25", "type": "zip", "shasum": "", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.10.0"}, "time": "2021-03-09T10:59:23+00:00", "require": {"php": "^7.2 || ^8.0", "symfony/polyfill-ctype": "^1.8"}}, {"version": "1.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "bafc69caeb4d49c39fd0779086c03a3738cbb389"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/bafc69caeb4d49c39fd0779086c03a3738cbb389", "type": "zip", "shasum": "", "reference": "bafc69caeb4d49c39fd0779086c03a3738cbb389"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.9.1"}, "time": "2020-07-08T17:02:28+00:00", "require": {"php": "^5.3.3 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^7.5.13"}, "conflict": {"vimeo/psalm": "<3.9.1", "phpstan/phpstan": "<0.12.20"}, "extra": "__unset"}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "9dc4f203e36f2b486149058bade43c851dd97451"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/9dc4f203e36f2b486149058bade43c851dd97451", "type": "zip", "shasum": "", "reference": "9dc4f203e36f2b486149058bade43c851dd97451"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.9.0"}, "time": "2020-06-16T10:16:42+00:00", "require": {"php": "^5.3.3 || ^7.0", "symfony/polyfill-ctype": "^1.8"}}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "ab2cb0b3b559010b75981b1bdce728da3ee90ad6"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/ab2cb0b3b559010b75981b1bdce728da3ee90ad6", "type": "zip", "shasum": "", "reference": "ab2cb0b3b559010b75981b1bdce728da3ee90ad6"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.8.0"}, "time": "2020-04-18T12:12:48+00:00", "conflict": {"vimeo/psalm": "<3.9.1"}}, {"version": "1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "aed98a490f9a8f78468232db345ab9cf606cf598"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/aed98a490f9a8f78468232db345ab9cf606cf598", "type": "zip", "shasum": "", "reference": "aed98a490f9a8f78468232db345ab9cf606cf598"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.7.0"}, "time": "2020-02-14T12:15:55+00:00", "conflict": {"vimeo/psalm": "<3.6.0"}}, {"version": "1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "573381c0a64f155a0d9a23f4b0c797194805b925"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/573381c0a64f155a0d9a23f4b0c797194805b925", "type": "zip", "shasum": "", "reference": "573381c0a64f155a0d9a23f4b0c797194805b925"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.6.0"}, "time": "2019-11-24T13:36:37+00:00"}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "88e6d84706d09a236046d686bbea96f07b3a34f4"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/88e6d84706d09a236046d686bbea96f07b3a34f4", "type": "zip", "shasum": "", "reference": "88e6d84706d09a236046d686bbea96f07b3a34f4"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.5.0"}, "time": "2019-08-24T08:43:50+00:00", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "conflict": "__unset"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "83e253c8e0be5b0257b881e1827274667c5c17a9"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/83e253c8e0be5b0257b881e1827274667c5c17a9", "type": "zip", "shasum": "", "reference": "83e253c8e0be5b0257b881e1827274667c5c17a9"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.4.0"}, "time": "2018-12-25T11:19:39+00:00", "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "0df1908962e7a3071564e857d86874dad1ef204a"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/0df1908962e7a3071564e857d86874dad1ef204a", "type": "zip", "shasum": "", "reference": "0df1908962e7a3071564e857d86874dad1ef204a"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.3.0"}, "time": "2018-01-29T19:49:41+00:00", "require": {"php": "^5.3.3 || ^7.0"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "2db61e59ff05fe5126d152bd0655c9ea113e550f"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/2db61e59ff05fe5126d152bd0655c9ea113e550f", "type": "zip", "shasum": "", "reference": "2db61e59ff05fe5126d152bd0655c9ea113e550f"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.2.0"}, "time": "2016-11-23T20:04:58+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "bb2d123231c095735130cc8f6d31385a44c7b308"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/bb2d123231c095735130cc8f6d31385a44c7b308", "type": "zip", "shasum": "", "reference": "bb2d123231c095735130cc8f6d31385a44c7b308"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.1.0"}, "time": "2016-08-09T15:02:57+00:00", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "require": {"php": "^5.3.3|^7.0"}}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "30eed06dd6bc88410a4ff7f77b6d22f3ce13dbde"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/30eed06dd6bc88410a4ff7f77b6d22f3ce13dbde", "type": "zip", "shasum": "", "reference": "30eed06dd6bc88410a4ff7f77b6d22f3ce13dbde"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.0.2"}, "time": "2015-08-24T13:29:44+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "^4.6"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "b8ef76d0f0c3b9a0a1bc987085fe0a0ddba984ab"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/b8ef76d0f0c3b9a0a1bc987085fe0a0ddba984ab", "type": "zip", "shasum": "", "reference": "b8ef76d0f0c3b9a0a1bc987085fe0a0ddba984ab"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.0.1"}, "time": "2015-05-12T15:19:25+00:00", "require-dev": "__unset"}, {"description": "Efficient assertions to validate the input/output of your methods.", "version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "3371442b05531d8490d0b51b90b55e61948b0f10"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/3371442b05531d8490d0b51b90b55e61948b0f10", "type": "zip", "shasum": "", "reference": "3371442b05531d8490d0b51b90b55e61948b0f10"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.0.0"}, "time": "2015-05-12T12:40:29+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "require": {"php": ">=5.3.9"}}, {"version": "1.0.0-beta", "version_normalized": "*******-beta", "source": {"url": "https://github.com/webmozarts/assert.git", "type": "git", "reference": "e0014a40f6a152293ee63d97b59d52e78ce7e098"}, "dist": {"url": "https://api.github.com/repos/webmozarts/assert/zipball/e0014a40f6a152293ee63d97b59d52e78ce7e098", "type": "zip", "shasum": "", "reference": "e0014a40f6a152293ee63d97b59d52e78ce7e098"}, "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.0.0-beta"}, "time": "2015-03-19T11:05:13+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}}]}, "security-advisories": [], "last-modified": "Sat, 30 Mar 2024 00:23:14 GMT"}