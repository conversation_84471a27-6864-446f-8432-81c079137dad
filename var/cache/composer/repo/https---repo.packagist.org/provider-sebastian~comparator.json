{"minified": "composer/2.0", "packages": {"sebastian/comparator": [{"name": "sebastian/comparator", "description": "Provides the functionality to compare PHP values for equality", "keywords": ["equality", "compare", "comparator"], "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "version": "7.1.3", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "dc904b4bb3ab070865fa4068cd84f3da8b945148"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/dc904b4bb3ab070865fa4068cd84f3da8b945148", "type": "zip", "shasum": "", "reference": "dc904b4bb3ab070865fa4068cd84f3da8b945148"}, "type": "library", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/7.1.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/comparator", "type": "tidelift"}], "time": "2025-08-20T11:27:00+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "7.1-dev"}}, "require": {"php": ">=8.3", "sebastian/diff": "^7.0", "sebastian/exporter": "^7.0", "ext-dom": "*", "ext-mbstring": "*"}, "require-dev": {"phpunit/phpunit": "^12.2"}, "suggest": {"ext-bcmath": "For comparing BcMath\\Number objects"}}, {"version": "7.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "1a7c2bce03a13a457ed3c975dfd331b3b4b133aa"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/1a7c2bce03a13a457ed3c975dfd331b3b4b133aa", "type": "zip", "shasum": "", "reference": "1a7c2bce03a13a457ed3c975dfd331b3b4b133aa"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/7.1.2"}, "time": "2025-08-10T08:50:08+00:00"}, {"version": "7.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "08dfa4684ca8fa2c83437b98d7bf8d9181a396d6"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/comparator/zipball/08dfa4684ca8fa2c83437b98d7bf8d9181a396d6", "type": "zip", "shasum": "", "reference": "08dfa4684ca8fa2c83437b98d7bf8d9181a396d6"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/7.1.1"}, "time": "2025-08-10T08:22:49+00:00"}, {"version": "7.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "03d905327dccc0851c9a08d6a979dfc683826b6f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/03d905327dccc0851c9a08d6a979dfc683826b6f", "type": "zip", "shasum": "", "reference": "03d905327dccc0851c9a08d6a979dfc683826b6f"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/7.1.0"}, "time": "2025-06-17T07:41:58+00:00"}, {"version": "7.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "b478f34614f934e0291598d0c08cbaba9644bee5"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/b478f34614f934e0291598d0c08cbaba9644bee5", "type": "zip", "shasum": "", "reference": "b478f34614f934e0291598d0c08cbaba9644bee5"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/7.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-03-07T07:00:32+00:00", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "require-dev": {"phpunit/phpunit": "^12.0"}}, {"version": "7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "18eb5a4f854dbd1d6512c459b605de2edb5a0b47"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/18eb5a4f854dbd1d6512c459b605de2edb5a0b47", "type": "zip", "shasum": "", "reference": "18eb5a4f854dbd1d6512c459b605de2edb5a0b47"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/7.0.0"}, "time": "2025-02-07T04:54:52+00:00"}, {"version": "6.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "85c77556683e6eee4323e4c5468641ca0237e2e8"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/comparator/zipball/85c77556683e6eee4323e4c5468641ca0237e2e8", "type": "zip", "shasum": "", "reference": "85c77556683e6eee4323e4c5468641ca0237e2e8"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/6.3.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/comparator", "type": "tidelift"}], "time": "2025-08-10T08:07:46+00:00", "extra": {"branch-alias": {"dev-main": "6.3-dev"}}, "require": {"php": ">=8.2", "sebastian/diff": "^6.0", "sebastian/exporter": "^6.0", "ext-dom": "*", "ext-mbstring": "*"}, "require-dev": {"phpunit/phpunit": "^11.4"}}, {"version": "6.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "24b8fbc2c8e201bb1308e7b05148d6ab393b6959"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/24b8fbc2c8e201bb1308e7b05148d6ab393b6959", "type": "zip", "shasum": "", "reference": "24b8fbc2c8e201bb1308e7b05148d6ab393b6959"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/6.3.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-03-07T06:57:01+00:00"}, {"version": "6.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "d4e47a769525c4dd38cea90e5dcd435ddbbc7115"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/d4e47a769525c4dd38cea90e5dcd435ddbbc7115", "type": "zip", "shasum": "", "reference": "d4e47a769525c4dd38cea90e5dcd435ddbbc7115"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/6.3.0"}, "time": "2025-01-06T10:28:19+00:00", "extra": {"branch-alias": {"dev-main": "6.2-dev"}}}, {"version": "6.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "43d129d6a0f81c78bee378b46688293eb7ea3739"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/comparator/zipball/43d129d6a0f81c78bee378b46688293eb7ea3739", "type": "zip", "shasum": "", "reference": "43d129d6a0f81c78bee378b46688293eb7ea3739"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/6.2.1"}, "time": "2024-10-31T05:30:08+00:00", "suggest": "__unset"}, {"version": "6.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "df95a6e827808debe3dbe190022dd0f643d5d909"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/df95a6e827808debe3dbe190022dd0f643d5d909", "type": "zip", "shasum": "", "reference": "df95a6e827808debe3dbe190022dd0f643d5d909"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/6.2.0"}, "time": "2024-10-30T13:12:44+00:00"}, {"version": "6.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "5ef523a49ae7a302b87b2102b72b1eda8918d686"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/5ef523a49ae7a302b87b2102b72b1eda8918d686", "type": "zip", "shasum": "", "reference": "5ef523a49ae7a302b87b2102b72b1eda8918d686"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/6.1.1"}, "time": "2024-10-18T15:00:48+00:00", "extra": {"branch-alias": {"dev-main": "6.1-dev"}}, "require-dev": {"phpunit/phpunit": "^11.3"}}, {"version": "6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "fa37b9e2ca618cb051d71b60120952ee8ca8b03d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/fa37b9e2ca618cb051d71b60120952ee8ca8b03d", "type": "zip", "shasum": "", "reference": "fa37b9e2ca618cb051d71b60120952ee8ca8b03d"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/6.1.0"}, "time": "2024-09-11T15:42:56+00:00"}, {"version": "6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "450d8f237bd611c45b5acf0733ce43e6bb280f81"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/450d8f237bd611c45b5acf0733ce43e6bb280f81", "type": "zip", "shasum": "", "reference": "450d8f237bd611c45b5acf0733ce43e6bb280f81"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/6.0.2"}, "time": "2024-08-12T06:07:25+00:00", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "require-dev": {"phpunit/phpunit": "^11.0"}}, {"version": "6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "131942b86d3587291067a94f295498ab6ac79c20"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/comparator/zipball/131942b86d3587291067a94f295498ab6ac79c20", "type": "zip", "shasum": "", "reference": "131942b86d3587291067a94f295498ab6ac79c20"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/6.0.1"}, "time": "2024-07-03T04:48:07+00:00"}, {"version": "6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "bd0f2fa5b9257c69903537b266ccb80fcf940db8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/bd0f2fa5b9257c69903537b266ccb80fcf940db8", "type": "zip", "shasum": "", "reference": "bd0f2fa5b9257c69903537b266ccb80fcf940db8"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/6.0.0"}, "time": "2024-02-02T05:53:45+00:00"}, {"version": "5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "e8e53097718d2b53cfb2aa859b06a41abf58c62e"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/e8e53097718d2b53cfb2aa859b06a41abf58c62e", "type": "zip", "shasum": "", "reference": "e8e53097718d2b53cfb2aa859b06a41abf58c62e"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/5.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/comparator", "type": "tidelift"}], "time": "2025-09-07T05:25:07+00:00", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "require": {"php": ">=8.1", "sebastian/diff": "^5.0", "sebastian/exporter": "^5.0", "ext-dom": "*", "ext-mbstring": "*"}, "require-dev": {"phpunit/phpunit": "^10.5"}}, {"version": "5.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e", "type": "zip", "shasum": "", "reference": "a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-10-18T14:56:07+00:00"}, {"version": "5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "2d3e04c3b4c1e84a5e7382221ad8883c8fbc4f53"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2d3e04c3b4c1e84a5e7382221ad8883c8fbc4f53", "type": "zip", "shasum": "", "reference": "2d3e04c3b4c1e84a5e7382221ad8883c8fbc4f53"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/5.0.2"}, "time": "2024-08-12T06:03:08+00:00", "require-dev": {"phpunit/phpunit": "^10.4"}}, {"version": "5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "2db5010a484d53ebf536087a70b4a5423c102372"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2db5010a484d53ebf536087a70b4a5423c102372", "type": "zip", "shasum": "", "reference": "2db5010a484d53ebf536087a70b4a5423c102372"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/5.0.1"}, "time": "2023-08-14T13:18:12+00:00", "require-dev": {"phpunit/phpunit": "^10.3"}}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "72f01e6586e0caf6af81297897bd112eb7e9627c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/72f01e6586e0caf6af81297897bd112eb7e9627c", "type": "zip", "shasum": "", "reference": "72f01e6586e0caf6af81297897bd112eb7e9627c"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/5.0.0"}, "time": "2023-02-03T07:07:16+00:00", "require-dev": {"phpunit/phpunit": "^10.0"}}, {"version": "4.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "67a2df3a62639eab2cc5906065e9805d4fd5dfc5"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/67a2df3a62639eab2cc5906065e9805d4fd5dfc5", "type": "zip", "shasum": "", "reference": "67a2df3a62639eab2cc5906065e9805d4fd5dfc5"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.9"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/comparator", "type": "tidelift"}], "time": "2025-08-10T06:51:50+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}}, {"version": "4.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "fa0f136dd2334583309d32b62544682ee972b51a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/fa0f136dd2334583309d32b62544682ee972b51a", "type": "zip", "shasum": "", "reference": "fa0f136dd2334583309d32b62544682ee972b51a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T12:41:17+00:00"}, {"version": "4.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "7fa545db548c90bdebeb9da0583001a252be5578"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/7fa545db548c90bdebeb9da0583001a252be5578", "type": "zip", "shasum": "", "reference": "7fa545db548c90bdebeb9da0583001a252be5578"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.7"}, "time": "2022-09-14T06:33:43+00:00"}, {"version": "4.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "55f4261989e546dc112258c7a75935a81a7ce382"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/55f4261989e546dc112258c7a75935a81a7ce382", "type": "zip", "shasum": "", "reference": "55f4261989e546dc112258c7a75935a81a7ce382"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/4.0.6"}, "time": "2020-10-26T15:49:45+00:00"}, {"version": "4.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "7a8ff306445707539c1a6397372a982a1ec55120"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/7a8ff306445707539c1a6397372a982a1ec55120", "type": "zip", "shasum": "", "reference": "7a8ff306445707539c1a6397372a982a1ec55120"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.5"}, "time": "2020-09-30T06:47:25+00:00"}, {"version": "4.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "e717aabeafe4eac045d3e947dad3207118664c72"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/e717aabeafe4eac045d3e947dad3207118664c72", "type": "zip", "shasum": "", "reference": "e717aabeafe4eac045d3e947dad3207118664c72"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.4"}, "time": "2020-09-28T05:31:46+00:00"}, {"version": "4.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "dcc580eadfaa4e7f9d2cf9ae1922134ea962e14f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/dcc580eadfaa4e7f9d2cf9ae1922134ea962e14f", "type": "zip", "shasum": "", "reference": "dcc580eadfaa4e7f9d2cf9ae1922134ea962e14f"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/master"}, "time": "2020-06-26T12:05:46+00:00", "require": {"php": "^7.3 || ^8.0", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}}, {"version": "4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "266d85ef789da8c41f06af4093c43e9798af2784"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/comparator/zipball/266d85ef789da8c41f06af4093c43e9798af2784", "type": "zip", "shasum": "", "reference": "266d85ef789da8c41f06af4093c43e9798af2784"}, "time": "2020-06-15T15:04:48+00:00", "require": {"php": "^7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "1de7b33e1d1fa4a58cb29b295c6e3349d73a6c4e"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/1de7b33e1d1fa4a58cb29b295c6e3349d73a6c4e", "type": "zip", "shasum": "", "reference": "1de7b33e1d1fa4a58cb29b295c6e3349d73a6c4e"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.1"}, "time": "2020-06-15T13:14:18+00:00"}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "85b3435da967696ed618ff745f32be3ff4a2b8e8"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/comparator/zipball/85b3435da967696ed618ff745f32be3ff4a2b8e8", "type": "zip", "shasum": "", "reference": "85b3435da967696ed618ff745f32be3ff4a2b8e8"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/master"}, "time": "2020-02-07T06:08:51+00:00", "funding": "__unset"}, {"version": "3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "4b3c947888c81708b20fb081bb653a2ba68f989a"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/comparator/zipball/4b3c947888c81708b20fb081bb653a2ba68f989a", "type": "zip", "shasum": "", "reference": "4b3c947888c81708b20fb081bb653a2ba68f989a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/comparator", "type": "tidelift"}], "time": "2025-08-10T05:29:24+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "require": {"php": ">=7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}}, {"version": "3.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "1dc7ceb4a24aede938c7af2a9ed1de09609ca770"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/1dc7ceb4a24aede938c7af2a9ed1de09609ca770", "type": "zip", "shasum": "", "reference": "1dc7ceb4a24aede938c7af2a9ed1de09609ca770"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/3.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T12:31:48+00:00"}, {"version": "3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "c05dd1878ec74058a28a569c59fc5c53a8cc1c7a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/c05dd1878ec74058a28a569c59fc5c53a8cc1c7a", "type": "zip", "shasum": "", "reference": "c05dd1878ec74058a28a569c59fc5c53a8cc1c7a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/3.0.4"}, "time": "2022-09-14T06:27:54+00:00"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "1071dfcef776a57013124ff35e1fc41ccd294758"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/1071dfcef776a57013124ff35e1fc41ccd294758", "type": "zip", "shasum": "", "reference": "1071dfcef776a57013124ff35e1fc41ccd294758"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/3.0.3"}, "time": "2020-11-30T08:04:30+00:00"}, {"version": "3.0.2", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "type": "zip", "shasum": "", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/master"}, "time": "2018-07-12T15:12:46+00:00", "require": {"php": "^7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "funding": "__unset"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "591a30922f54656695e59b1f39501aec513403da"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON><PERSON>/comparator/zipball/591a30922f54656695e59b1f39501aec513403da", "type": "zip", "shasum": "", "reference": "591a30922f54656695e59b1f39501aec513403da"}, "time": "2018-06-14T15:05:28+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "ed5fd2281113729f1ebcc64d101ad66028aeb3d5"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/ed5fd2281113729f1ebcc64d101ad66028aeb3d5", "type": "zip", "shasum": "", "reference": "ed5fd2281113729f1ebcc64d101ad66028aeb3d5"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/3.0.0"}, "time": "2018-04-18T13:33:00+00:00"}, {"version": "2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "34369daee48eafb2651bea869b4b15d75ccc35f9"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/34369daee48eafb2651bea869b4b15d75ccc35f9", "type": "zip", "shasum": "", "reference": "34369daee48eafb2651bea869b4b15d75ccc35f9"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/master"}, "time": "2018-02-01T13:46:46+00:00", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "require": {"php": "^7.0", "sebastian/diff": "^2.0 || ^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^6.4"}}, {"version": "2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "11c07feade1d65453e06df3b3b90171d6d982087"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/11c07feade1d65453e06df3b3b90171d6d982087", "type": "zip", "shasum": "", "reference": "11c07feade1d65453e06df3b3b90171d6d982087"}, "time": "2018-01-12T06:34:42+00:00", "require": {"php": "^7.0", "sebastian/diff": "^2.0", "sebastian/exporter": "^3.1"}}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "b11c729f95109b56a0fe9650c6a63a0fcd8c439f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/b11c729f95109b56a0fe9650c6a63a0fcd8c439f", "type": "zip", "shasum": "", "reference": "b11c729f95109b56a0fe9650c6a63a0fcd8c439f"}, "time": "2017-12-22T14:50:35+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "1174d9018191e93cb9d719edec01257fc05f8158"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/1174d9018191e93cb9d719edec01257fc05f8158", "type": "zip", "shasum": "", "reference": "1174d9018191e93cb9d719edec01257fc05f8158"}, "time": "2017-11-03T07:16:52+00:00"}, {"homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "ae068fede81d06e7bb9bb46a367210a3d3e1fe6a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/ae068fede81d06e7bb9bb46a367210a3d3e1fe6a", "type": "zip", "shasum": "", "reference": "ae068fede81d06e7bb9bb46a367210a3d3e1fe6a"}, "time": "2017-08-03T07:14:59+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require": {"php": "^7.0", "sebastian/diff": "^2.0", "sebastian/exporter": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "875bd7cdcb5f49e9bcea732538767937abbed51d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/875bd7cdcb5f49e9bcea732538767937abbed51d", "type": "zip", "shasum": "", "reference": "875bd7cdcb5f49e9bcea732538767937abbed51d"}, "time": "2017-07-11T16:29:53+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "20f84f468cb67efee293246e6a09619b891f55f0"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/20f84f468cb67efee293246e6a09619b891f55f0", "type": "zip", "shasum": "", "reference": "20f84f468cb67efee293246e6a09619b891f55f0"}, "time": "2017-03-03T06:26:08+00:00", "require": {"php": "^7.0", "sebastian/diff": "^1.2", "sebastian/exporter": "^3.0"}}, {"version": "1.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "type": "zip", "shasum": "", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/1.2"}, "time": "2017-01-29T09:50:25+00:00", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2 || ~2.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}}, {"version": "1.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "6554a8aff8735fcd859b44279d5c03a6d1b1cb23"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/6554a8aff8735fcd859b44279d5c03a6d1b1cb23", "type": "zip", "shasum": "", "reference": "6554a8aff8735fcd859b44279d5c03a6d1b1cb23"}, "time": "2017-01-29T07:17:51+00:00"}, {"version": "1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "6a1ed12e8b2409076ab22e3897126211ff8b1f7f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/6a1ed12e8b2409076ab22e3897126211ff8b1f7f", "type": "zip", "shasum": "", "reference": "6a1ed12e8b2409076ab22e3897126211ff8b1f7f"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/master"}, "time": "2016-11-19T09:18:40+00:00"}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "ce2bda23a56456f19e35d98241446b581f648c14"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/ce2bda23a56456f19e35d98241446b581f648c14", "type": "zip", "shasum": "", "reference": "ce2bda23a56456f19e35d98241446b581f648c14"}, "time": "2016-11-17T14:39:37+00:00", "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "937efb279bd37a375bcadf584dec0726f84dbf22"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/937efb279bd37a375bcadf584dec0726f84dbf22", "type": "zip", "shasum": "", "reference": "937efb279bd37a375bcadf584dec0726f84dbf22"}, "time": "2015-07-26T15:48:44+00:00"}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "dee3fd1828677b2182c076ad2caecbb5c7bdbf1c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/dee3fd1828677b2182c076ad2caecbb5c7bdbf1c", "type": "zip", "shasum": "", "reference": "dee3fd1828677b2182c076ad2caecbb5c7bdbf1c"}, "time": "2015-07-19T12:54:49+00:00", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "1dd8869519a225f7f2b9eb663e225298fade819e"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/comparator/zipball/1dd8869519a225f7f2b9eb663e225298fade819e", "type": "zip", "shasum": "", "reference": "1dd8869519a225f7f2b9eb663e225298fade819e"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/1.1.1"}, "time": "2015-01-29T16:28:08+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "c484a80f97573ab934e37826dba0135a3301b26a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/c484a80f97573ab934e37826dba0135a3301b26a", "type": "zip", "shasum": "", "reference": "c484a80f97573ab934e37826dba0135a3301b26a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/master"}, "time": "2014-11-16T21:32:38+00:00", "require": {"php": ">=5.3.3", "sebastian/diff": "~1.1", "sebastian/exporter": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.1"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "e54a01c0da1b87db3c5a3c4c5277ddf331da4aef"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/e54a01c0da1b87db3c5a3c4c5277ddf331da4aef", "type": "zip", "shasum": "", "reference": "e54a01c0da1b87db3c5a3c4c5277ddf331da4aef"}, "time": "2014-05-11T23:00:21+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}, {"version": "1.0.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/sebastian<PERSON>mann/comparator.git", "type": "git", "reference": "f7069ee51fa9fb6c038e16a9d0e3439f5449dcf2"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/f7069ee51fa9fb6c038e16a9d0e3439f5449dcf2", "type": "zip", "shasum": "", "reference": "f7069ee51fa9fb6c038e16a9d0e3439f5449dcf2"}, "time": "2014-05-02T07:05:58+00:00"}]}, "security-advisories": [], "last-modified": "Sun, 07 Sep 2025 05:25:40 GMT"}