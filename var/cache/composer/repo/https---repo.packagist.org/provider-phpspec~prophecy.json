{"minified": "composer/2.0", "packages": {"phpspec/prophecy": [{"name": "phpspec/prophecy", "description": "Highly opinionated mocking framework for PHP 5.3+", "keywords": ["mock", "stub", "fake", "dev", "spy", "Dummy", "Double"], "homepage": "https://github.com/phpspec/prophecy", "version": "v1.22.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "35f1adb388946d92e6edab2aa2cb2b60e132ebd5"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/35f1adb388946d92e6edab2aa2cb2b60e132ebd5", "type": "zip", "shasum": "", "reference": "35f1adb388946d92e6edab2aa2cb2b60e132ebd5"}, "type": "library", "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.22.0"}, "funding": [], "time": "2025-04-29T14:58:06+00:00", "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "require": {"php": "^7.4 || 8.0.* || 8.1.* || 8.2.* || 8.3.* || 8.4.*", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0", "doctrine/instantiator": "^1.2 || ^2.0", "sebastian/recursion-context": "^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.40", "phpspec/phpspec": "^6.0 || ^7.0", "phpstan/phpstan": "^2.1.13", "phpunit/phpunit": "^8.0 || ^9.0 || ^10.0"}}, {"version": "v1.21.0", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "7594ec2f6507dd6a5feaf4fa50c391d5274a5838"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/7594ec2f6507dd6a5feaf4fa50c391d5274a5838", "type": "zip", "shasum": "", "reference": "7594ec2f6507dd6a5feaf4fa50c391d5274a5838"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.21.0"}, "time": "2025-04-29T11:13:33+00:00", "require": {"php": "^7.2 || 8.0.* || 8.1.* || 8.2.* || 8.3.* || 8.4.*", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0", "doctrine/instantiator": "^1.2 || ^2.0", "sebastian/recursion-context": "^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.40", "phpspec/phpspec": "^6.0 || ^7.0", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^8.0 || ^9.0 || ^10.0"}}, {"version": "v1.20.0", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "a0165c648cab6a80311c74ffc708a07bb53ecc93"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/a0165c648cab6a80311c74ffc708a07bb53ecc93", "type": "zip", "shasum": "", "reference": "a0165c648cab6a80311c74ffc708a07bb53ecc93"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.20.0"}, "time": "2024-11-19T13:12:41+00:00", "require": {"php": "^7.2 || 8.0.* || 8.1.* || 8.2.* || 8.3.* || 8.4.*", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0 || ^5.0 || ^6.0", "doctrine/instantiator": "^1.2 || ^2.0", "sebastian/recursion-context": "^3.0 || ^4.0 || ^5.0 || ^6.0"}}, {"version": "v1.19.0", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "67a759e7d8746d501c41536ba40cd9c0a07d6a87"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/67a759e7d8746d501c41536ba40cd9c0a07d6a87", "type": "zip", "shasum": "", "reference": "67a759e7d8746d501c41536ba40cd9c0a07d6a87"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.19.0"}, "time": "2024-02-29T11:52:51+00:00", "require": {"php": "^7.2 || 8.0.* || 8.1.* || 8.2.* || 8.3.*", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0 || ^5.0 || ^6.0", "doctrine/instantiator": "^1.2 || ^2.0", "sebastian/recursion-context": "^3.0 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"phpspec/phpspec": "^6.0 || ^7.0", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^8.0 || ^9.0 || ^10.0"}}, {"version": "v1.18.0", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "d4f454f7e1193933f04e6500de3e79191648ed0c"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/d4f454f7e1193933f04e6500de3e79191648ed0c", "type": "zip", "shasum": "", "reference": "d4f454f7e1193933f04e6500de3e79191648ed0c"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.18.0"}, "time": "2023-12-07T16:22:33+00:00", "require": {"php": "^7.2 || 8.0.* || 8.1.* || 8.2.* || 8.3.*", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0 || ^5.0", "doctrine/instantiator": "^1.2 || ^2.0", "sebastian/recursion-context": "^3.0 || ^4.0 || ^5.0"}}, {"keywords": ["mock", "stub", "fake", "spy", "Dummy", "Double"], "version": "v1.17.0", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "15873c65b207b07765dbc3c95d20fdf4a320cbe2"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/15873c65b207b07765dbc3c95d20fdf4a320cbe2", "type": "zip", "shasum": "", "reference": "15873c65b207b07765dbc3c95d20fdf4a320cbe2"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.17.0"}, "time": "2023-02-02T15:41:36+00:00", "require": {"php": "^7.2 || 8.0.* || 8.1.* || 8.2.*", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0", "doctrine/instantiator": "^1.2 || ^2.0", "sebastian/recursion-context": "^3.0 || ^4.0"}, "require-dev": {"phpspec/phpspec": "^6.0 || ^7.0", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^8.0 || ^9.0"}}, {"version": "v1.16.0", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "be8cac52a0827776ff9ccda8c381ac5b71aeb359"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/be8cac52a0827776ff9ccda8c381ac5b71aeb359", "type": "zip", "shasum": "", "reference": "be8cac52a0827776ff9ccda8c381ac5b71aeb359"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.16.0"}, "time": "2022-11-29T15:06:56+00:00", "require": {"php": "^7.2 || 8.0.* || 8.1.* || 8.2.*", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0", "doctrine/instantiator": "^1.2", "sebastian/recursion-context": "^3.0 || ^4.0"}, "require-dev": {"phpspec/phpspec": "^6.0 || ^7.0", "phpunit/phpunit": "^8.0 || ^9.0"}}, {"version": "v1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "bbcd7380b0ebf3961ee21409db7b38bc31d69a13"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/bbcd7380b0ebf3961ee21409db7b38bc31d69a13", "type": "zip", "shasum": "", "reference": "bbcd7380b0ebf3961ee21409db7b38bc31d69a13"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.15.0"}, "time": "2021-12-08T12:19:24+00:00", "require": {"php": "^7.2 || ~8.0, <8.2", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0", "doctrine/instantiator": "^1.2", "sebastian/recursion-context": "^3.0 || ^4.0"}}, {"version": "1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "d86dfc2e2a3cd366cee475e52c6bb3bbc371aa0e"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/d86dfc2e2a3cd366cee475e52c6bb3bbc371aa0e", "type": "zip", "shasum": "", "reference": "d86dfc2e2a3cd366cee475e52c6bb3bbc371aa0e"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/1.14.0"}, "time": "2021-09-10T09:02:12+00:00"}, {"version": "1.13.0", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "be1996ed8adc35c3fd795488a653f4b518be70ea"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/be1996ed8adc35c3fd795488a653f4b518be70ea", "type": "zip", "shasum": "", "reference": "be1996ed8adc35c3fd795488a653f4b518be70ea"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/1.13.0"}, "time": "2021-03-17T13:42:18+00:00", "extra": {"branch-alias": {"dev-master": "1.11.x-dev"}}, "require": {"php": "^7.2 || ~8.0, <8.1", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0", "doctrine/instantiator": "^1.2", "sebastian/recursion-context": "^3.0 || ^4.0"}, "require-dev": {"phpspec/phpspec": "^6.0", "phpunit/phpunit": "^8.0 || ^9.0"}}, {"version": "1.12.2", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "245710e971a030f42e08f4912863805570f23d39"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/245710e971a030f42e08f4912863805570f23d39", "type": "zip", "shasum": "", "reference": "245710e971a030f42e08f4912863805570f23d39"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/1.12.2"}, "time": "2020-12-19T10:15:11+00:00"}, {"version": "1.12.1", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "8ce87516be71aae9b956f81906aaf0338e0d8a2d"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/8ce87516be71aae9b956f81906aaf0338e0d8a2d", "type": "zip", "shasum": "", "reference": "8ce87516be71aae9b956f81906aaf0338e0d8a2d"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/1.12.1"}, "time": "2020-09-29T09:10:42+00:00", "require-dev": {"phpspec/phpspec": "^6.0", "phpunit/phpunit": "^8.0 || ^9.0 <9.3"}}, {"version": "1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "765cd5d5d237525f8bbadaec5dc161c83a369119"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/765cd5d5d237525f8bbadaec5dc161c83a369119", "type": "zip", "shasum": "", "reference": "765cd5d5d237525f8bbadaec5dc161c83a369119"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/1.12.0"}, "time": "2020-09-28T12:23:07+00:00"}, {"version": "1.11.1", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "b20034be5efcdab4fb60ca3a29cba2949aead160"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/b20034be5efcdab4fb60ca3a29cba2949aead160", "type": "zip", "shasum": "", "reference": "b20034be5efcdab4fb60ca3a29cba2949aead160"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/master"}, "time": "2020-07-08T12:44:21+00:00", "require": {"php": "^7.2", "phpdocumentor/reflection-docblock": "^5.0", "sebastian/comparator": "^3.0 || ^4.0", "doctrine/instantiator": "^1.2", "sebastian/recursion-context": "^3.0 || ^4.0"}, "require-dev": {"phpspec/phpspec": "^6.0", "phpunit/phpunit": "^8.0"}}, {"version": "1.11.0", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "8ff0384cd5d87e038297e79d85c99e4b2dcf0e61"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/8ff0384cd5d87e038297e79d85c99e4b2dcf0e61", "type": "zip", "shasum": "", "reference": "8ff0384cd5d87e038297e79d85c99e4b2dcf0e61"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/1.11.0"}, "time": "2020-07-07T16:07:38+00:00"}, {"version": "v1.10.3", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "451c3cd1418cf640de218914901e51b064abb093"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/451c3cd1418cf640de218914901e51b064abb093", "type": "zip", "shasum": "", "reference": "451c3cd1418cf640de218914901e51b064abb093"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.10.3"}, "time": "2020-03-05T15:02:03+00:00", "extra": {"branch-alias": {"dev-master": "1.10.x-dev"}}, "require": {"php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0|^5.0", "sebastian/comparator": "^1.2.3|^2.0|^3.0|^4.0", "doctrine/instantiator": "^1.0.2", "sebastian/recursion-context": "^1.0|^2.0|^3.0|^4.0"}, "require-dev": {"phpspec/phpspec": "^2.5 || ^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}}, {"version": "v1.10.2", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "b4400efc9d206e83138e2bb97ed7f5b14b831cd9"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/b4400efc9d206e83138e2bb97ed7f5b14b831cd9", "type": "zip", "shasum": "", "reference": "b4400efc9d206e83138e2bb97ed7f5b14b831cd9"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.10.2"}, "time": "2020-01-20T15:57:02+00:00", "funding": "__unset"}, {"version": "1.10.1", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "cbe1df668b3fe136bcc909126a0f529a78d4cbbc"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/cbe1df668b3fe136bcc909126a0f529a78d4cbbc", "type": "zip", "shasum": "", "reference": "cbe1df668b3fe136bcc909126a0f529a78d4cbbc"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/master"}, "time": "2019-12-22T21:05:45+00:00", "require": {"php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0|^5.0", "sebastian/comparator": "^1.2.3|^2.0|^3.0", "doctrine/instantiator": "^1.0.2", "sebastian/recursion-context": "^1.0|^2.0|^3.0"}}, {"version": "1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "d638ebbb58daba25a6a0dc7969e1358a0e3c6682"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/d638ebbb58daba25a6a0dc7969e1358a0e3c6682", "type": "zip", "shasum": "", "reference": "d638ebbb58daba25a6a0dc7969e1358a0e3c6682"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/1.10.0"}, "time": "2019-12-17T16:54:23+00:00"}, {"version": "1.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "f6811d96d97bdf400077a0cc100ae56aa32b9203"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/f6811d96d97bdf400077a0cc100ae56aa32b9203", "type": "zip", "shasum": "", "reference": "f6811d96d97bdf400077a0cc100ae56aa32b9203"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/master"}, "time": "2019-10-03T11:07:50+00:00", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "require": {"php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0|^5.0", "sebastian/comparator": "^1.1|^2.0|^3.0", "doctrine/instantiator": "^1.0.2", "sebastian/recursion-context": "^1.0|^2.0|^3.0"}, "require-dev": {"phpspec/phpspec": "^2.5|^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}}, {"version": "1.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "1927e75f4ed19131ec9bcc3b002e07fb1173ee76"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/1927e75f4ed19131ec9bcc3b002e07fb1173ee76", "type": "zip", "shasum": "", "reference": "1927e75f4ed19131ec9bcc3b002e07fb1173ee76"}, "time": "2019-06-13T12:50:23+00:00", "require": {"php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0", "sebastian/comparator": "^1.1|^2.0|^3.0", "doctrine/instantiator": "^1.0.2", "sebastian/recursion-context": "^1.0|^2.0|^3.0"}}, {"version": "1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "4ba436b55987b4bf311cb7c6ba82aa528aac0a06"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/4ba436b55987b4bf311cb7c6ba82aa528aac0a06", "type": "zip", "shasum": "", "reference": "4ba436b55987b4bf311cb7c6ba82aa528aac0a06"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/1.8.0"}, "time": "2018-08-05T17:53:17+00:00", "autoload": {"psr-0": {"Prophecy\\": "src/"}}}, {"version": "1.7.6", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "33a7e3c4fda54e912ff6338c48823bd5c0f0b712"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/33a7e3c4fda54e912ff6338c48823bd5c0f0b712", "type": "zip", "shasum": "", "reference": "33a7e3c4fda54e912ff6338c48823bd5c0f0b712"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/master"}, "time": "2018-04-18T13:57:24+00:00", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "require-dev": {"phpspec/phpspec": "^2.5|^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5"}}, {"version": "1.7.5", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "dfd6be44111a7c41c2e884a336cc4f461b3b2401"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/dfd6be44111a7c41c2e884a336cc4f461b3b2401", "type": "zip", "shasum": "", "reference": "dfd6be44111a7c41c2e884a336cc4f461b3b2401"}, "time": "2018-02-19T10:16:54+00:00", "require": {"php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0", "sebastian/comparator": "^1.1|^2.0", "doctrine/instantiator": "^1.0.2", "sebastian/recursion-context": "^1.0|^2.0|^3.0"}}, {"version": "1.7.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "9f901e29c93dae4aa77c0bb161df4276f9c9a1be"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/9f901e29c93dae4aa77c0bb161df4276f9c9a1be", "type": "zip", "shasum": "", "reference": "9f901e29c93dae4aa77c0bb161df4276f9c9a1be"}, "time": "2018-02-11T18:49:29+00:00"}, {"version": "1.7.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "e4ed002c67da8eceb0eb8ddb8b3847bb53c5c2bf"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/e4ed002c67da8eceb0eb8ddb8b3847bb53c5c2bf", "type": "zip", "shasum": "", "reference": "e4ed002c67da8eceb0eb8ddb8b3847bb53c5c2bf"}, "time": "2017-11-24T13:59:53+00:00", "require-dev": {"phpspec/phpspec": "^2.5|^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7"}}, {"version": "v1.7.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "c9b8c6088acd19d769d4cc0ffa60a9fe34344bd6"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/c9b8c6088acd19d769d4cc0ffa60a9fe34344bd6", "type": "zip", "shasum": "", "reference": "c9b8c6088acd19d769d4cc0ffa60a9fe34344bd6"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.7.2"}, "time": "2017-09-04T11:05:03+00:00", "require-dev": {"phpspec/phpspec": "^2.5|^3.2", "phpunit/phpunit": "^4.8 || ^5.6.5"}}, {"version": "v1.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "15ea9ac619e37009edcda64089e3fa4cc88aa659"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/15ea9ac619e37009edcda64089e3fa4cc88aa659", "type": "zip", "shasum": "", "reference": "15ea9ac619e37009edcda64089e3fa4cc88aa659"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/master"}, "time": "2017-09-03T09:38:53+00:00"}, {"version": "v1.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "93d39f1f7f9326d746203c7c056f300f7f126073"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/93d39f1f7f9326d746203c7c056f300f7f126073", "type": "zip", "shasum": "", "reference": "93d39f1f7f9326d746203c7c056f300f7f126073"}, "time": "2017-03-02T20:05:34+00:00", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "require": {"php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2", "sebastian/comparator": "^1.1|^2.0", "doctrine/instantiator": "^1.0.2", "sebastian/recursion-context": "^1.0|^2.0|^3.0"}}, {"version": "v1.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "6c52c2722f8460122f96f86346600e1077ce22cb"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/6c52c2722f8460122f96f86346600e1077ce22cb", "type": "zip", "shasum": "", "reference": "6c52c2722f8460122f96f86346600e1077ce22cb"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.6.2"}, "time": "2016-11-21T14:58:47+00:00", "require": {"php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2", "sebastian/comparator": "^1.1", "doctrine/instantiator": "^1.0.2", "sebastian/recursion-context": "^1.0|^2.0"}, "require-dev": {"phpspec/phpspec": "^2.0", "phpunit/phpunit": "^4.8 || ^5.6.5"}}, {"version": "v1.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "58a8137754bc24b25740d4281399a4a3596058e0"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/58a8137754bc24b25740d4281399a4a3596058e0", "type": "zip", "shasum": "", "reference": "58a8137754bc24b25740d4281399a4a3596058e0"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.6.1"}, "time": "2016-06-07T08:13:47+00:00", "require": {"php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2", "sebastian/comparator": "^1.1", "doctrine/instantiator": "^1.0.2", "sebastian/recursion-context": "^1.0"}, "require-dev": {"phpspec/phpspec": "^2.0"}}, {"version": "v1.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "3c91bdf81797d725b14cb62906f9a4ce44235972"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/3c91bdf81797d725b14cb62906f9a4ce44235972", "type": "zip", "shasum": "", "reference": "3c91bdf81797d725b14cb62906f9a4ce44235972"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/master"}, "time": "2016-02-15T07:46:21+00:00", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "require": {"php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "~2.0", "sebastian/comparator": "~1.1", "doctrine/instantiator": "^1.0.2", "sebastian/recursion-context": "~1.0"}, "require-dev": {"phpspec/phpspec": "~2.0"}}, {"version": "v1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "4745ded9307786b730d7a60df5cb5a6c43cf95f7"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/4745ded9307786b730d7a60df5cb5a6c43cf95f7", "type": "zip", "shasum": "", "reference": "4745ded9307786b730d7a60df5cb5a6c43cf95f7"}, "time": "2015-08-13T10:07:40+00:00", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "require": {"phpdocumentor/reflection-docblock": "~2.0", "sebastian/comparator": "~1.1", "doctrine/instantiator": "^1.0.2"}}, {"version": "v1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "3132b1f44c7bf2ec4c7eb2d3cb78fdeca760d373"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/3132b1f44c7bf2ec4c7eb2d3cb78fdeca760d373", "type": "zip", "shasum": "", "reference": "3132b1f44c7bf2ec4c7eb2d3cb78fdeca760d373"}, "time": "2015-04-27T22:15:08+00:00"}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "8724cd239f8ef4c046f55a3b18b4d91cc7f3e4c5"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/8724cd239f8ef4c046f55a3b18b4d91cc7f3e4c5", "type": "zip", "shasum": "", "reference": "8724cd239f8ef4c046f55a3b18b4d91cc7f3e4c5"}, "time": "2015-03-27T19:31:25+00:00"}, {"homepage": "http://phpspec.org", "version": "v1.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "9ca52329bcdd1500de24427542577ebf3fc2f1c9"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/9ca52329bcdd1500de24427542577ebf3fc2f1c9", "type": "zip", "shasum": "", "reference": "9ca52329bcdd1500de24427542577ebf3fc2f1c9"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.3.1"}, "time": "2014-11-17T16:23:49+00:00", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "require": {"phpdocumentor/reflection-docblock": "~2.0", "doctrine/instantiator": "~1.0,>=1.0.2"}}, {"version": "v1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "91c2e9b44e5899738e1949dc248370cabdd5a9ae"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/91c2e9b44e5899738e1949dc248370cabdd5a9ae", "type": "zip", "shasum": "", "reference": "91c2e9b44e5899738e1949dc248370cabdd5a9ae"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/master"}, "time": "2014-11-14T15:31:18+00:00"}, {"version": "v1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "ed9c0bcffe4639a5b0ff83e399eeb28e7fcc68f0"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/ed9c0bcffe4639a5b0ff83e399eeb28e7fcc68f0", "type": "zip", "shasum": "", "reference": "ed9c0bcffe4639a5b0ff83e399eeb28e7fcc68f0"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.2.1"}, "time": "2014-09-23T10:52:18+00:00"}, {"version": "v1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "328484c88ed3cd7d1980b624bb98fa635f212ec9"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/328484c88ed3cd7d1980b624bb98fa635f212ec9", "type": "zip", "shasum": "", "reference": "328484c88ed3cd7d1980b624bb98fa635f212ec9"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/master"}, "time": "2014-07-18T21:26:55+00:00", "require": {"phpdocumentor/reflection-docblock": "~2.0"}}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "976a65af02a2a0e17ce6c949f7b43437205628bb"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/976a65af02a2a0e17ce6c949f7b43437205628bb", "type": "zip", "shasum": "", "reference": "976a65af02a2a0e17ce6c949f7b43437205628bb"}, "time": "2014-01-24T11:03:43+00:00", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "require-dev": {"phpspec/phpspec": "2.0.*"}, "require": "__unset"}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "fc5ddee4879dfcee788d9cb4b92af9028bc3d7e8"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/fc5ddee4879dfcee788d9cb4b92af9028bc3d7e8", "type": "zip", "shasum": "", "reference": "fc5ddee4879dfcee788d9cb4b92af9028bc3d7e8"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.1.1"}, "time": "2014-01-15T15:06:06+00:00"}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "119b07de9c592a0286cd584d6e961903ba096d8a"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/119b07de9c592a0286cd584d6e961903ba096d8a", "type": "zip", "shasum": "", "reference": "119b07de9c592a0286cd584d6e961903ba096d8a"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.1.0"}, "time": "2014-01-01T21:15:36+00:00"}, {"version": "v1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "79d9c8bd94801bffbf9b56964f6438762da6d8cd"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/79d9c8bd94801bffbf9b56964f6438762da6d8cd", "type": "zip", "shasum": "", "reference": "79d9c8bd94801bffbf9b56964f6438762da6d8cd"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/master"}, "time": "2013-08-10T11:11:45+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}, {"version": "v1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "83ba2c1ca1d8a62bdf8b2cf92a9cc44aa19fb3a8"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/83ba2c1ca1d8a62bdf8b2cf92a9cc44aa19fb3a8", "type": "zip", "shasum": "", "reference": "83ba2c1ca1d8a62bdf8b2cf92a9cc44aa19fb3a8"}, "time": "2013-07-04T20:06:04+00:00"}, {"version": "v1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "3403012d07a0b55eeaddedef334a22f422cc3fd4"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/3403012d07a0b55eeaddedef334a22f422cc3fd4", "type": "zip", "shasum": "", "reference": "3403012d07a0b55eeaddedef334a22f422cc3fd4"}, "time": "2013-05-19T10:12:33+00:00"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "de83666fe7f9cb3b8b256e1a627527ec7dca1e4e"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/de83666fe7f9cb3b8b256e1a627527ec7dca1e4e", "type": "zip", "shasum": "", "reference": "de83666fe7f9cb3b8b256e1a627527ec7dca1e4e"}, "time": "2013-04-30T08:47:47+00:00", "require-dev": {"phpspec/phpspec2": "dev-develop"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "a92cfa21cfddb392473ecc8f981b7529fb30d02c"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/a92cfa21cfddb392473ecc8f981b7529fb30d02c", "type": "zip", "shasum": "", "reference": "a92cfa21cfddb392473ecc8f981b7529fb30d02c"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.0.0"}, "time": "2013-04-29T15:35:46+00:00"}, {"version": "v1.0.0-BETA2", "version_normalized": "*******-beta2", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "c423d8122bba960f3a976b2aabf66268656c3700"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/c423d8122bba960f3a976b2aabf66268656c3700", "type": "zip", "shasum": "", "reference": "c423d8122bba960f3a976b2aabf66268656c3700"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/master"}, "time": "2013-04-03T09:25:21+00:00"}, {"version": "v1.0.0-BETA1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/phpspec/prophecy.git", "type": "git", "reference": "c9d2101d94b71d00f394d6b088026836abc1d2ce"}, "dist": {"url": "https://api.github.com/repos/phpspec/prophecy/zipball/c9d2101d94b71d00f394d6b088026836abc1d2ce", "type": "zip", "shasum": "", "reference": "c9d2101d94b71d00f394d6b088026836abc1d2ce"}, "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.0.0-BETA1"}, "time": "2013-03-25T11:41:31+00:00", "autoload": {"psr-0": {"Prophecy": "src/"}}, "extra": "__unset"}]}, "security-advisories": [], "last-modified": "<PERSON><PERSON>, 29 Apr 2025 14:59:24 GMT"}