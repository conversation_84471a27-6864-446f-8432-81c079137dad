{"minified": "composer/2.0", "packages": {"phpunit/php-code-coverage": [{"name": "phpunit/php-code-coverage", "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "keywords": ["testing", "coverage", "xunit"], "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "version": "12.3.6", "version_normalized": "********", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "da2cdaff87220fa641e7652364281b736e4347e0"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/da2cdaff87220fa641e7652364281b736e4347e0", "type": "zip", "shasum": "", "reference": "da2cdaff87220fa641e7652364281b736e4347e0"}, "type": "library", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.3.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/php-code-coverage", "type": "tidelift"}], "time": "2025-09-02T05:23:14+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "12.3.x-dev"}}, "require": {"php": ">=8.3", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^5.6.1", "phpunit/php-file-iterator": "^6.0", "phpunit/php-text-template": "^5.0", "sebastian/complexity": "^5.0", "sebastian/environment": "^8.0.3", "sebastian/lines-of-code": "^4.0", "sebastian/version": "^6.0", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^12.3.7"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}}, {"version": "12.3.5", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "96dc0466673e215bf5536301039017f03cd45c6b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/96dc0466673e215bf5536301039017f03cd45c6b", "type": "zip", "shasum": "", "reference": "96dc0466673e215bf5536301039017f03cd45c6b"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.3.5"}, "time": "2025-09-01T08:07:42+00:00"}, {"version": "12.3.4", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "7ad0e9bdc72b147600badccd694a2e57ffc9297a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/7ad0e9bdc72b147600badccd694a2e57ffc9297a", "type": "zip", "shasum": "", "reference": "7ad0e9bdc72b147600badccd694a2e57ffc9297a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.3.4"}, "time": "2025-08-29T11:32:44+00:00", "require": {"php": ">=8.3", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^5.4.0", "phpunit/php-file-iterator": "^6.0", "phpunit/php-text-template": "^5.0", "sebastian/complexity": "^5.0", "sebastian/environment": "^8.0", "sebastian/lines-of-code": "^4.0", "sebastian/version": "^6.0", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^12.1"}}, {"version": "12.3.3", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "733025d94635a001f67db71a2ed1bab4e7e4a9dc"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/733025d94635a001f67db71a2ed1bab4e7e4a9dc", "type": "zip", "shasum": "", "reference": "733025d94635a001f67db71a2ed1bab4e7e4a9dc"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.3.3"}, "time": "2025-08-27T14:43:48+00:00"}, {"version": "12.3.2", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "086553c5b2e0e1e20293d782d788ab768202b621"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/086553c5b2e0e1e20293d782d788ab768202b621", "type": "zip", "shasum": "", "reference": "086553c5b2e0e1e20293d782d788ab768202b621"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.3.2"}, "time": "2025-07-29T06:19:24+00:00"}, {"version": "12.3.1", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "ddec29dfc128eba9c204389960f2063f3b7fa170"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ddec29dfc128eba9c204389960f2063f3b7fa170", "type": "zip", "shasum": "", "reference": "ddec29dfc128eba9c204389960f2063f3b7fa170"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.3.1"}, "time": "2025-06-18T08:58:13+00:00"}, {"version": "12.3.0", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "9075a8efc66e11bc55c319062e147bdb06777267"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/9075a8efc66e11bc55c319062e147bdb06777267", "type": "zip", "shasum": "", "reference": "9075a8efc66e11bc55c319062e147bdb06777267"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.3.0"}, "time": "2025-05-23T15:49:03+00:00"}, {"version": "12.2.1", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "448f2c504d86dbff3949dcd02c95aa85db2c7617"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/448f2c504d86dbff3949dcd02c95aa85db2c7617", "type": "zip", "shasum": "", "reference": "448f2c504d86dbff3949dcd02c95aa85db2c7617"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.2.1"}, "time": "2025-05-04T05:25:05+00:00", "extra": {"branch-alias": {"dev-main": "12.2.x-dev"}}}, {"version": "12.2.0", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "4c7133dbade8423ef124ae62c39a075ba591cb3f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/4c7133dbade8423ef124ae62c39a075ba591cb3f", "type": "zip", "shasum": "", "reference": "4c7133dbade8423ef124ae62c39a075ba591cb3f"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.2.0"}, "time": "2025-05-03T07:27:30+00:00"}, {"version": "12.1.2", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "05c33d01a856f9f62488d144bafddc3d7b7a4ebb"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/05c33d01a856f9f62488d144bafddc3d7b7a4ebb", "type": "zip", "shasum": "", "reference": "05c33d01a856f9f62488d144bafddc3d7b7a4ebb"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-04-03T14:34:39+00:00", "extra": {"branch-alias": {"dev-main": "12.1.x-dev"}}, "require-dev": {"phpunit/phpunit": "^12.0"}}, {"version": "12.1.1", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "0ce76cf0940abbc31525420b7b8d174656c6675d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/0ce76cf0940abbc31525420b7b8d174656c6675d", "type": "zip", "shasum": "", "reference": "0ce76cf0940abbc31525420b7b8d174656c6675d"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.1.1"}, "time": "2025-04-03T03:41:27+00:00"}, {"version": "12.1.0", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "d331a5ced3d9a2b917baa9841b2211e72f9e780d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/d331a5ced3d9a2b917baa9841b2211e72f9e780d", "type": "zip", "shasum": "", "reference": "d331a5ced3d9a2b917baa9841b2211e72f9e780d"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.1.0"}, "time": "2025-03-17T13:56:07+00:00"}, {"version": "12.0.5", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "da732486790670c622aa367eb3dbb7e1619e68be"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/da732486790670c622aa367eb3dbb7e1619e68be", "type": "zip", "shasum": "", "reference": "da732486790670c622aa367eb3dbb7e1619e68be"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.0.5"}, "time": "2025-03-15T07:24:51+00:00", "extra": {"branch-alias": {"dev-main": "12.0.x-dev"}}}, {"version": "12.0.4", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "79e5ef5897068689c7c325554d6df905480ce942"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/79e5ef5897068689c7c325554d6df905480ce942", "type": "zip", "shasum": "", "reference": "79e5ef5897068689c7c325554d6df905480ce942"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.0.4"}, "time": "2025-02-25T13:27:48+00:00"}, {"version": "12.0.3", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "2e3038bff41d56114e5396151060f5ac9d2d6751"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/2e3038bff41d56114e5396151060f5ac9d2d6751", "type": "zip", "shasum": "", "reference": "2e3038bff41d56114e5396151060f5ac9d2d6751"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.0.3"}, "time": "2025-02-18T14:04:13+00:00"}, {"version": "12.0.2", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "69dde560510151f7d04315fac6c72016cc5d7bc8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/69dde560510151f7d04315fac6c72016cc5d7bc8", "type": "zip", "shasum": "", "reference": "69dde560510151f7d04315fac6c72016cc5d7bc8"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.0.2"}, "time": "2025-02-08T09:08:00+00:00"}, {"version": "12.0.1", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "89d8cb213336dfd15f134bd4ebfd9a420bba6024"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/89d8cb213336dfd15f134bd4ebfd9a420bba6024", "type": "zip", "shasum": "", "reference": "89d8cb213336dfd15f134bd4ebfd9a420bba6024"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.0.1"}, "time": "2025-02-07T13:45:04+00:00"}, {"version": "12.0.0", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "b1fbe7ded00c951a3507b2352bd6c30574fd9672"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/b1fbe7ded00c951a3507b2352bd6c30574fd9672", "type": "zip", "shasum": "", "reference": "b1fbe7ded00c951a3507b2352bd6c30574fd9672"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.0.0"}, "time": "2025-02-07T05:01:24+00:00"}, {"version": "11.0.11", "version_normalized": "*********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "4f7722aa9a7b76aa775e2d9d4e95d1ea16eeeef4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/4f7722aa9a7b76aa775e2d9d4e95d1ea16eeeef4", "type": "zip", "shasum": "", "reference": "4f7722aa9a7b76aa775e2d9d4e95d1ea16eeeef4"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/11.0.11"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/php-code-coverage", "type": "tidelift"}], "time": "2025-08-27T14:37:49+00:00", "extra": {"branch-alias": {"dev-main": "11.0.x-dev"}}, "require": {"php": ">=8.2", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^5.4.0", "phpunit/php-file-iterator": "^5.1.0", "phpunit/php-text-template": "^4.0.1", "sebastian/code-unit-reverse-lookup": "^4.0.1", "sebastian/complexity": "^4.0.1", "sebastian/environment": "^7.2.0", "sebastian/lines-of-code": "^3.0.1", "sebastian/version": "^5.0.2", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^11.5.2"}}, {"version": "11.0.10", "version_normalized": "*********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "1a800a7446add2d79cc6b3c01c45381810367d76"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/1a800a7446add2d79cc6b3c01c45381810367d76", "type": "zip", "shasum": "", "reference": "1a800a7446add2d79cc6b3c01c45381810367d76"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/show"}, "time": "2025-06-18T08:56:18+00:00"}, {"version": "11.0.9", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "14d63fbcca18457e49c6f8bebaa91a87e8e188d7"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/14d63fbcca18457e49c6f8bebaa91a87e8e188d7", "type": "zip", "shasum": "", "reference": "14d63fbcca18457e49c6f8bebaa91a87e8e188d7"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/11.0.9"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-25T13:26:39+00:00"}, {"version": "11.0.8", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "418c59fd080954f8c4aa5631d9502ecda2387118"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/418c59fd080954f8c4aa5631d9502ecda2387118", "type": "zip", "shasum": "", "reference": "418c59fd080954f8c4aa5631d9502ecda2387118"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/11.0.8"}, "time": "2024-12-11T12:34:27+00:00", "require": {"php": ">=8.2", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^5.3.1", "phpunit/php-file-iterator": "^5.1.0", "phpunit/php-text-template": "^4.0.1", "sebastian/code-unit-reverse-lookup": "^4.0.1", "sebastian/complexity": "^4.0.1", "sebastian/environment": "^7.2.0", "sebastian/lines-of-code": "^3.0.1", "sebastian/version": "^5.0.2", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^11.5.0"}}, {"version": "11.0.7", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "f7f08030e8811582cc459871d28d6f5a1a4d35ca"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/f7f08030e8811582cc459871d28d6f5a1a4d35ca", "type": "zip", "shasum": "", "reference": "f7f08030e8811582cc459871d28d6f5a1a4d35ca"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/11.0.7"}, "time": "2024-10-09T06:21:38+00:00", "require-dev": {"phpunit/phpunit": "^11.4.1"}}, {"version": "11.0.6", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "ebdffc9e09585dafa71b9bffcdb0a229d4704c45"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ebdffc9e09585dafa71b9bffcdb0a229d4704c45", "type": "zip", "shasum": "", "reference": "ebdffc9e09585dafa71b9bffcdb0a229d4704c45"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/11.0.6"}, "time": "2024-08-22T04:37:56+00:00", "require": {"php": ">=8.2", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^5.1.0", "phpunit/php-file-iterator": "^5.0.1", "phpunit/php-text-template": "^4.0.1", "sebastian/code-unit-reverse-lookup": "^4.0.1", "sebastian/complexity": "^4.0.1", "sebastian/environment": "^7.2.0", "sebastian/lines-of-code": "^3.0.1", "sebastian/version": "^5.0.1", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^11.0"}}, {"version": "11.0.5", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "19b6365ab8b59a64438c0c3f4241feeb480c9861"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/19b6365ab8b59a64438c0c3f4241feeb480c9861", "type": "zip", "shasum": "", "reference": "19b6365ab8b59a64438c0c3f4241feeb480c9861"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/11.0.5"}, "time": "2024-07-03T05:05:37+00:00", "extra": {"branch-alias": {"dev-main": "11.0-dev"}}, "require": {"php": ">=8.2", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^5.0", "phpunit/php-file-iterator": "^5.0", "phpunit/php-text-template": "^4.0", "sebastian/code-unit-reverse-lookup": "^4.0", "sebastian/complexity": "^4.0", "sebastian/environment": "^7.0", "sebastian/lines-of-code": "^3.0", "sebastian/version": "^5.0", "theseer/tokenizer": "^1.2.0"}}, {"version": "11.0.4", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "4dc2b7a606073f0fb80da09842ffb068b627c38f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/4dc2b7a606073f0fb80da09842ffb068b627c38f", "type": "zip", "shasum": "", "reference": "4dc2b7a606073f0fb80da09842ffb068b627c38f"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/11.0.4"}, "time": "2024-06-29T08:26:25+00:00"}, {"version": "11.0.3", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "7e35a2cbcabac0e6865fd373742ea432a3c34f92"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/7e35a2cbcabac0e6865fd373742ea432a3c34f92", "type": "zip", "shasum": "", "reference": "7e35a2cbcabac0e6865fd373742ea432a3c34f92"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/11.0.3"}, "time": "2024-03-12T15:35:40+00:00"}, {"version": "11.0.2", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "9e0a298b4dc6438a1e70ac8e1b3ea4980ae5a09b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/9e0a298b4dc6438a1e70ac8e1b3ea4980ae5a09b", "type": "zip", "shasum": "", "reference": "9e0a298b4dc6438a1e70ac8e1b3ea4980ae5a09b"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/11.0.2"}, "time": "2024-03-09T16:56:49+00:00"}, {"version": "11.0.1", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "89702be0ad026873ef3a1605fe8726254eef4e2c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/89702be0ad026873ef3a1605fe8726254eef4e2c", "type": "zip", "shasum": "", "reference": "89702be0ad026873ef3a1605fe8726254eef4e2c"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/11.0.1"}, "time": "2024-03-02T07:34:25+00:00"}, {"version": "11.0.0", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "5e238e4b982cb272bf9faeee6f33af83d465d0e2"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/5e238e4b982cb272bf9faeee6f33af83d465d0e2", "type": "zip", "shasum": "", "reference": "5e238e4b982cb272bf9faeee6f33af83d465d0e2"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/11.0.0"}, "time": "2024-02-02T06:03:46+00:00"}, {"version": "10.1.16", "version_normalized": "*********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "7e308268858ed6baedc8704a304727d20bc07c77"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/7e308268858ed6baedc8704a304727d20bc07c77", "type": "zip", "shasum": "", "reference": "7e308268858ed6baedc8704a304727d20bc07c77"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.16"}, "time": "2024-08-22T04:31:57+00:00", "extra": {"branch-alias": {"dev-main": "10.1.x-dev"}}, "require": {"php": ">=8.1", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-text-template": "^3.0.1", "sebastian/code-unit-reverse-lookup": "^3.0.0", "sebastian/complexity": "^3.2.0", "sebastian/environment": "^6.1.0", "sebastian/lines-of-code": "^2.0.2", "sebastian/version": "^4.0.1", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^10.1"}}, {"version": "10.1.15", "version_normalized": "*********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "5da8b1728acd1e6ffdf2ff32ffbdfd04307f26ae"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/5da8b1728acd1e6ffdf2ff32ffbdfd04307f26ae", "type": "zip", "shasum": "", "reference": "5da8b1728acd1e6ffdf2ff32ffbdfd04307f26ae"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.15"}, "time": "2024-06-29T08:25:15+00:00", "extra": {"branch-alias": {"dev-main": "10.1-dev"}}, "require": {"php": ">=8.1", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.18 || ^5.0", "phpunit/php-file-iterator": "^4.0", "phpunit/php-text-template": "^3.0", "sebastian/code-unit-reverse-lookup": "^3.0", "sebastian/complexity": "^3.0", "sebastian/environment": "^6.0", "sebastian/lines-of-code": "^2.0", "sebastian/version": "^4.0", "theseer/tokenizer": "^1.2.0"}}, {"version": "10.1.14", "version_normalized": "*********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "e3f51450ebffe8e0efdf7346ae966a656f7d5e5b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/e3f51450ebffe8e0efdf7346ae966a656f7d5e5b", "type": "zip", "shasum": "", "reference": "e3f51450ebffe8e0efdf7346ae966a656f7d5e5b"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.14"}, "time": "2024-03-12T15:33:41+00:00"}, {"version": "10.1.13", "version_normalized": "*********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "d51c3aec14896d5e80b354fad58e998d1980f8f8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/d51c3aec14896d5e80b354fad58e998d1980f8f8", "type": "zip", "shasum": "", "reference": "d51c3aec14896d5e80b354fad58e998d1980f8f8"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.13"}, "time": "2024-03-09T16:54:15+00:00"}, {"version": "10.1.12", "version_normalized": "*********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "842f72662d6b9edda84c4b6f13885fd9cd53dc63"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/842f72662d6b9edda84c4b6f13885fd9cd53dc63", "type": "zip", "shasum": "", "reference": "842f72662d6b9edda84c4b6f13885fd9cd53dc63"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.12"}, "time": "2024-03-02T07:22:05+00:00"}, {"version": "10.1.11", "version_normalized": "*********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "78c3b7625965c2513ee96569a4dbb62601784145"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/78c3b7625965c2513ee96569a4dbb62601784145", "type": "zip", "shasum": "", "reference": "78c3b7625965c2513ee96569a4dbb62601784145"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.11"}, "time": "2023-12-21T15:38:30+00:00"}, {"version": "10.1.10", "version_normalized": "*********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "599109c8ca6bae97b23482d557d2874c25a65e59"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/599109c8ca6bae97b23482d557d2874c25a65e59", "type": "zip", "shasum": "", "reference": "599109c8ca6bae97b23482d557d2874c25a65e59"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.10"}, "time": "2023-12-11T06:28:43+00:00", "require": {"php": ">=8.1", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.15", "phpunit/php-file-iterator": "^4.0", "phpunit/php-text-template": "^3.0", "sebastian/code-unit-reverse-lookup": "^3.0", "sebastian/complexity": "^3.0", "sebastian/environment": "^6.0", "sebastian/lines-of-code": "^2.0", "sebastian/version": "^4.0", "theseer/tokenizer": "^1.2.0"}}, {"version": "10.1.9", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "a56a9ab2f680246adcf3db43f38ddf1765774735"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/a56a9ab2f680246adcf3db43f38ddf1765774735", "type": "zip", "shasum": "", "reference": "a56a9ab2f680246adcf3db43f38ddf1765774735"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.9"}, "time": "2023-11-23T12:23:20+00:00"}, {"version": "10.1.8", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "84838eed9ded511f61dc3e8b5944a52d9017b297"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/84838eed9ded511f61dc3e8b5944a52d9017b297", "type": "zip", "shasum": "", "reference": "84838eed9ded511f61dc3e8b5944a52d9017b297"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.8"}, "time": "2023-11-15T13:31:15+00:00"}, {"version": "10.1.7", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "355324ca4980b8916c18b9db29f3ef484078f26e"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/355324ca4980b8916c18b9db29f3ef484078f26e", "type": "zip", "shasum": "", "reference": "355324ca4980b8916c18b9db29f3ef484078f26e"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.7"}, "time": "2023-10-04T15:34:17+00:00"}, {"version": "10.1.6", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "56f33548fe522c8d82da7ff3824b42829d324364"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/56f33548fe522c8d82da7ff3824b42829d324364", "type": "zip", "shasum": "", "reference": "56f33548fe522c8d82da7ff3824b42829d324364"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.6"}, "time": "2023-09-19T04:59:03+00:00"}, {"version": "10.1.5", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "1df504e42a88044c27a90136910f0b3fe9e91939"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/1df504e42a88044c27a90136910f0b3fe9e91939", "type": "zip", "shasum": "", "reference": "1df504e42a88044c27a90136910f0b3fe9e91939"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.5"}, "time": "2023-09-12T14:37:22+00:00"}, {"version": "10.1.4", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "cd59bb34756a16ca8253ce9b2909039c227fff71"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/cd59bb34756a16ca8253ce9b2909039c227fff71", "type": "zip", "shasum": "", "reference": "cd59bb34756a16ca8253ce9b2909039c227fff71"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.4"}, "time": "2023-08-31T14:04:38+00:00"}, {"version": "10.1.3", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "be1fe461fdc917de2a29a452ccf2657d325b443d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/be1fe461fdc917de2a29a452ccf2657d325b443d", "type": "zip", "shasum": "", "reference": "be1fe461fdc917de2a29a452ccf2657d325b443d"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.3"}, "time": "2023-07-26T13:45:28+00:00"}, {"version": "10.1.2", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "db1497ec8dd382e82c962f7abbe0320e4882ee4e"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/db1497ec8dd382e82c962f7abbe0320e4882ee4e", "type": "zip", "shasum": "", "reference": "db1497ec8dd382e82c962f7abbe0320e4882ee4e"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.2"}, "time": "2023-05-22T09:04:27+00:00"}, {"version": "10.1.1", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "884a0da7f9f46f28b2cb69134217fd810b793974"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/884a0da7f9f46f28b2cb69134217fd810b793974", "type": "zip", "shasum": "", "reference": "884a0da7f9f46f28b2cb69134217fd810b793974"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.1"}, "time": "2023-04-17T12:15:40+00:00"}, {"version": "10.1.0", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "fc4f5ee614fa82d50ecf9014b51af0a9561f3df8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/fc4f5ee614fa82d50ecf9014b51af0a9561f3df8", "type": "zip", "shasum": "", "reference": "fc4f5ee614fa82d50ecf9014b51af0a9561f3df8"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.0"}, "time": "2023-04-13T07:08:27+00:00", "require-dev": {"phpunit/phpunit": "^10.0"}}, {"version": "10.0.2", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "20800e84296ea4732f9a125e08ce86b4004ae3e4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/20800e84296ea4732f9a125e08ce86b4004ae3e4", "type": "zip", "shasum": "", "reference": "20800e84296ea4732f9a125e08ce86b4004ae3e4"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.0.2"}, "time": "2023-03-06T13:00:19+00:00", "extra": {"branch-alias": {"dev-main": "10.0-dev"}}}, {"version": "10.0.1", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "b9c21a93dd8c8eed79879374884ee733259475cc"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/b9c21a93dd8c8eed79879374884ee733259475cc", "type": "zip", "shasum": "", "reference": "b9c21a93dd8c8eed79879374884ee733259475cc"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.0.1"}, "time": "2023-02-25T05:35:03+00:00", "suggest": {"ext-pcov": "*", "ext-xdebug": "*"}}, {"version": "10.0.0", "version_normalized": "10.0.0.0", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "bf4fbc9c13af7da12b3ea807574fb460f255daba"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/bf4fbc9c13af7da12b3ea807574fb460f255daba", "type": "zip", "shasum": "", "reference": "bf4fbc9c13af7da12b3ea807574fb460f255daba"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.0.0"}, "time": "2023-02-03T07:14:34+00:00", "require": {"php": ">=8.1", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.14", "phpunit/php-file-iterator": "^4.0", "phpunit/php-text-template": "^3.0", "sebastian/code-unit-reverse-lookup": "^3.0", "sebastian/complexity": "^3.0", "sebastian/environment": "^6.0", "sebastian/lines-of-code": "^2.0", "sebastian/version": "^4.0", "theseer/tokenizer": "^1.2.0"}}, {"version": "9.2.32", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "85402a822d1ecf1db1096959413d35e1c37cf1a5"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/85402a822d1ecf1db1096959413d35e1c37cf1a5", "type": "zip", "shasum": "", "reference": "85402a822d1ecf1db1096959413d35e1c37cf1a5"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.32"}, "time": "2024-08-22T04:23:01+00:00", "extra": {"branch-alias": {"dev-main": "9.2.x-dev"}}, "require": {"php": ">=7.3", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-text-template": "^2.0.4", "sebastian/code-unit-reverse-lookup": "^2.0.3", "sebastian/complexity": "^2.0.3", "sebastian/environment": "^5.1.5", "sebastian/lines-of-code": "^1.0.4", "sebastian/version": "^3.0.2", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}}, {"version": "9.2.31", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "48c34b5d8d983006bd2adc2d0de92963b9155965"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/48c34b5d8d983006bd2adc2d0de92963b9155965", "type": "zip", "shasum": "", "reference": "48c34b5d8d983006bd2adc2d0de92963b9155965"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.31"}, "time": "2024-03-02T06:37:42+00:00", "extra": {"branch-alias": {"dev-master": "9.2-dev"}}, "require": {"php": ">=7.3", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.18 || ^5.0", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0.3", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}}, {"version": "9.2.30", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "ca2bd87d2f9215904682a9cb9bb37dda98e76089"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ca2bd87d2f9215904682a9cb9bb37dda98e76089", "type": "zip", "shasum": "", "reference": "ca2bd87d2f9215904682a9cb9bb37dda98e76089"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.30"}, "time": "2023-12-22T06:47:57+00:00"}, {"version": "9.2.29", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "6a3a87ac2bbe33b25042753df8195ba4aa534c76"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/6a3a87ac2bbe33b25042753df8195ba4aa534c76", "type": "zip", "shasum": "", "reference": "6a3a87ac2bbe33b25042753df8195ba4aa534c76"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.29"}, "time": "2023-09-19T04:57:46+00:00", "require": {"php": ">=7.3", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.15", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0.3", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}}, {"version": "9.2.28", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "7134a5ccaaf0f1c92a4f5501a6c9f98ac4dcc0ef"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/7134a5ccaaf0f1c92a4f5501a6c9f98ac4dcc0ef", "type": "zip", "shasum": "", "reference": "7134a5ccaaf0f1c92a4f5501a6c9f98ac4dcc0ef"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.28"}, "time": "2023-09-12T14:36:20+00:00"}, {"version": "9.2.27", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "b0a88255cb70d52653d80c890bd7f38740ea50d1"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/b0a88255cb70d52653d80c890bd7f38740ea50d1", "type": "zip", "shasum": "", "reference": "b0a88255cb70d52653d80c890bd7f38740ea50d1"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.27"}, "time": "2023-07-26T13:44:30+00:00"}, {"version": "9.2.26", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "443bc6912c9bd5b409254a40f4b0f4ced7c80ea1"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/443bc6912c9bd5b409254a40f4b0f4ced7c80ea1", "type": "zip", "shasum": "", "reference": "443bc6912c9bd5b409254a40f4b0f4ced7c80ea1"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.26"}, "time": "2023-03-06T12:58:08+00:00"}, {"version": "9.2.25", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "0e2b40518197a8c0d4b08bc34dfff1c99c508954"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/0e2b40518197a8c0d4b08bc34dfff1c99c508954", "type": "zip", "shasum": "", "reference": "0e2b40518197a8c0d4b08bc34dfff1c99c508954"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.25"}, "time": "2023-02-25T05:32:00+00:00", "suggest": {"ext-pcov": "*", "ext-xdebug": "*"}}, {"version": "9.2.24", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "2cf940ebc6355a9d430462811b5aaa308b174bed"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/2cf940ebc6355a9d430462811b5aaa308b174bed", "type": "zip", "shasum": "", "reference": "2cf940ebc6355a9d430462811b5aaa308b174bed"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.24"}, "time": "2023-01-26T08:26:55+00:00", "require": {"php": ">=7.3", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.14", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0.3", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}}, {"version": "9.2.23", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "9f1f0f9a2fbb680b26d1cf9b61b6eac43a6e4e9c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/9f1f0f9a2fbb680b26d1cf9b61b6eac43a6e4e9c", "type": "zip", "shasum": "", "reference": "9f1f0f9a2fbb680b26d1cf9b61b6eac43a6e4e9c"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.23"}, "time": "2022-12-28T12:41:10+00:00"}, {"version": "9.2.22", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "e4bf60d2220b4baaa0572986b5d69870226b06df"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/e4bf60d2220b4baaa0572986b5d69870226b06df", "type": "zip", "shasum": "", "reference": "e4bf60d2220b4baaa0572986b5d69870226b06df"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.22"}, "time": "2022-12-18T16:40:55+00:00"}, {"version": "9.2.21", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "3f893e19712bb0c8bc86665d1562e9fd509c4ef0"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/3f893e19712bb0c8bc86665d1562e9fd509c4ef0", "type": "zip", "shasum": "", "reference": "3f893e19712bb0c8bc86665d1562e9fd509c4ef0"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.21"}, "time": "2022-12-14T13:26:54+00:00"}, {"version": "9.2.20", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "af7463c955007de36db0c5e26d03e2f933c2e980"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/af7463c955007de36db0c5e26d03e2f933c2e980", "type": "zip", "shasum": "", "reference": "af7463c955007de36db0c5e26d03e2f933c2e980"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.20"}, "time": "2022-12-13T07:49:28+00:00"}, {"version": "9.2.19", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "c77b56b63e3d2031bd8997fcec43c1925ae46559"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/c77b56b63e3d2031bd8997fcec43c1925ae46559", "type": "zip", "shasum": "", "reference": "c77b56b63e3d2031bd8997fcec43c1925ae46559"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.19"}, "time": "2022-11-18T07:47:47+00:00"}, {"version": "9.2.18", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "12fddc491826940cf9b7e88ad9664cf51f0f6d0a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/12fddc491826940cf9b7e88ad9664cf51f0f6d0a", "type": "zip", "shasum": "", "reference": "12fddc491826940cf9b7e88ad9664cf51f0f6d0a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.18"}, "time": "2022-10-27T13:35:33+00:00"}, {"version": "9.2.17", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "aa94dc41e8661fe90c7316849907cba3007b10d8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/aa94dc41e8661fe90c7316849907cba3007b10d8", "type": "zip", "shasum": "", "reference": "aa94dc41e8661fe90c7316849907cba3007b10d8"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.17"}, "time": "2022-08-30T12:24:04+00:00"}, {"version": "9.2.16", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "2593003befdcc10db5e213f9f28814f5aa8ac073"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/2593003befdcc10db5e213f9f28814f5aa8ac073", "type": "zip", "shasum": "", "reference": "2593003befdcc10db5e213f9f28814f5aa8ac073"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.16"}, "time": "2022-08-20T05:26:47+00:00"}, {"version": "9.2.15", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "2e9da11878c4202f97915c1cb4bb1ca318a63f5f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/2e9da11878c4202f97915c1cb4bb1ca318a63f5f", "type": "zip", "shasum": "", "reference": "2e9da11878c4202f97915c1cb4bb1ca318a63f5f"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.15"}, "time": "2022-03-07T09:28:20+00:00", "require": {"php": ">=7.3", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.13.0", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0.3", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}}, {"version": "9.2.14", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "9f4d60b6afe5546421462b76cd4e633ebc364ab4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/9f4d60b6afe5546421462b76cd4e633ebc364ab4", "type": "zip", "shasum": "", "reference": "9f4d60b6afe5546421462b76cd4e633ebc364ab4"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.14"}, "time": "2022-02-28T12:38:02+00:00"}, {"version": "9.2.13", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "deac8540cb7bd40b2b8cfa679b76202834fd04e8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/deac8540cb7bd40b2b8cfa679b76202834fd04e8", "type": "zip", "shasum": "", "reference": "deac8540cb7bd40b2b8cfa679b76202834fd04e8"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.13"}, "time": "2022-02-23T17:02:38+00:00"}, {"version": "9.2.11", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "665a1ac0a763c51afc30d6d130dac0813092b17f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/665a1ac0a763c51afc30d6d130dac0813092b17f", "type": "zip", "shasum": "", "reference": "665a1ac0a763c51afc30d6d130dac0813092b17f"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.11"}, "time": "2022-02-18T12:46:09+00:00"}, {"version": "9.2.10", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "d5850aaf931743067f4bfc1ae4cbd06468400687"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/d5850aaf931743067f4bfc1ae4cbd06468400687", "type": "zip", "shasum": "", "reference": "d5850aaf931743067f4bfc1ae4cbd06468400687"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.10"}, "time": "2021-12-05T09:12:13+00:00"}, {"version": "9.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "f301eb1453c9e7a1bc912ee8b0ea9db22c60223b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/f301eb1453c9e7a1bc912ee8b0ea9db22c60223b", "type": "zip", "shasum": "", "reference": "f301eb1453c9e7a1bc912ee8b0ea9db22c60223b"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.9"}, "time": "2021-11-19T15:21:02+00:00"}, {"version": "9.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "cf04e88a2e3c56fc1a65488afd493325b4c1bc3e"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/cf04e88a2e3c56fc1a65488afd493325b4c1bc3e", "type": "zip", "shasum": "", "reference": "cf04e88a2e3c56fc1a65488afd493325b4c1bc3e"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.8"}, "time": "2021-10-30T08:01:38+00:00"}, {"version": "9.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "d4c798ed8d51506800b441f7a13ecb0f76f12218"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/d4c798ed8d51506800b441f7a13ecb0f76f12218", "type": "zip", "shasum": "", "reference": "d4c798ed8d51506800b441f7a13ecb0f76f12218"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.7"}, "time": "2021-09-17T05:39:03+00:00", "require": {"php": ">=7.3", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.12.0", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0.3", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}}, {"version": "9.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "f6293e1b30a2354e8428e004689671b83871edde"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/f6293e1b30a2354e8428e004689671b83871edde", "type": "zip", "shasum": "", "reference": "f6293e1b30a2354e8428e004689671b83871edde"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.6"}, "time": "2021-03-28T07:26:59+00:00", "require": {"php": ">=7.3", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.10.2", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0.3", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}}, {"version": "9.2.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "f3e026641cc91909d421802dd3ac7827ebfd97e1"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/f3e026641cc91909d421802dd3ac7827ebfd97e1", "type": "zip", "shasum": "", "reference": "f3e026641cc91909d421802dd3ac7827ebfd97e1"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.5"}, "time": "2020-11-28T06:44:49+00:00"}, {"version": "9.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "0a7f0acf9269c190fd982b5c04423feae986b6e0"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/0a7f0acf9269c190fd982b5c04423feae986b6e0", "type": "zip", "shasum": "", "reference": "0a7f0acf9269c190fd982b5c04423feae986b6e0"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.4"}, "time": "2020-11-27T06:15:15+00:00", "require": {"php": ">=7.3", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.10.2", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}}, {"version": "9.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "6b20e2055f7c29b56cb3870b3de7cc463d7add41"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/6b20e2055f7c29b56cb3870b3de7cc463d7add41", "type": "zip", "shasum": "", "reference": "6b20e2055f7c29b56cb3870b3de7cc463d7add41"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.3"}, "time": "2020-10-30T10:46:41+00:00"}, {"version": "9.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "229099d7d69fe5044f6e464f669b1fd34f252e8d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/229099d7d69fe5044f6e464f669b1fd34f252e8d", "type": "zip", "shasum": "", "reference": "229099d7d69fe5044f6e464f669b1fd34f252e8d"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.2"}, "time": "2020-10-28T06:24:13+00:00", "require": {"php": ">=7.3", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.8", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}}, {"version": "9.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "ed363c3ce393560a1c300dce0298bbf0f0528b13"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ed363c3ce393560a1c300dce0298bbf0f0528b13", "type": "zip", "shasum": "", "reference": "ed363c3ce393560a1c300dce0298bbf0f0528b13"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.1"}, "time": "2020-10-26T15:46:21+00:00"}, {"version": "9.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "53a4b737e83be724efd2bc4e7b929b9a30c48972"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/53a4b737e83be724efd2bc4e7b929b9a30c48972", "type": "zip", "shasum": "", "reference": "53a4b737e83be724efd2bc4e7b929b9a30c48972"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.0"}, "time": "2020-10-02T03:37:32+00:00"}, {"version": "9.1.11", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "c9394cb9d07ecfa9351b96f2e296bad473195f4d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/c9394cb9d07ecfa9351b96f2e296bad473195f4d", "type": "zip", "shasum": "", "reference": "c9394cb9d07ecfa9351b96f2e296bad473195f4d"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.1.11"}, "time": "2020-09-19T05:29:17+00:00", "extra": {"branch-alias": {"dev-master": "9.1-dev"}}}, {"version": "9.1.10", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "5134e2a65ec48e2f7126d12f1503a9889c660bdf"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/5134e2a65ec48e2f7126d12f1503a9889c660bdf", "type": "zip", "shasum": "", "reference": "5134e2a65ec48e2f7126d12f1503a9889c660bdf"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.1.10"}, "time": "2020-09-18T05:25:29+00:00"}, {"version": "9.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "cf6582906d4b2502c5f4c6be6a3d0b4cd5b3ef7f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/cf6582906d4b2502c5f4c6be6a3d0b4cd5b3ef7f", "type": "zip", "shasum": "", "reference": "cf6582906d4b2502c5f4c6be6a3d0b4cd5b3ef7f"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.1.9"}, "time": "2020-09-15T06:14:11+00:00"}, {"version": "9.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "f98f8466126d83b55b924a94d2244c53c216b8fb"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/f98f8466126d83b55b924a94d2244c53c216b8fb", "type": "zip", "shasum": "", "reference": "f98f8466126d83b55b924a94d2244c53c216b8fb"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2020-09-07T08:07:10+00:00", "require": {"php": "^7.3 || ^8.0", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.8", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}}, {"version": "9.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "2ef92bec3186a827faf7362ff92ae4e8ec2e49d2"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/2ef92bec3186a827faf7362ff92ae4e8ec2e49d2", "type": "zip", "shasum": "", "reference": "2ef92bec3186a827faf7362ff92ae4e8ec2e49d2"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.1.7"}, "time": "2020-09-03T07:09:19+00:00"}, {"version": "9.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "d25b24b1cd14772bde4d75daeb393dc17db9f6e6"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/d25b24b1cd14772bde4d75daeb393dc17db9f6e6", "type": "zip", "shasum": "", "reference": "d25b24b1cd14772bde4d75daeb393dc17db9f6e6"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2020-08-31T06:31:46+00:00"}, {"version": "9.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "adea70610c070869261d2d0a62fa968611447b56"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/adea70610c070869261d2d0a62fa968611447b56", "type": "zip", "shasum": "", "reference": "adea70610c070869261d2d0a62fa968611447b56"}, "time": "2020-08-27T06:29:01+00:00"}, {"version": "9.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "4422fca28c3634e2de8c7c373af97a104dd1a45f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/4422fca28c3634e2de8c7c373af97a104dd1a45f", "type": "zip", "shasum": "", "reference": "4422fca28c3634e2de8c7c373af97a104dd1a45f"}, "time": "2020-08-13T15:04:53+00:00"}, {"version": "9.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "4abbce3b0ad05f2e7143ea5f775d5513cb5261e4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/4abbce3b0ad05f2e7143ea5f775d5513cb5261e4", "type": "zip", "shasum": "", "reference": "4abbce3b0ad05f2e7143ea5f775d5513cb5261e4"}, "time": "2020-08-10T17:45:51+00:00"}, {"version": "9.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "5210f9269e44e6d34419cb92242ef11aee9351ac"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/5210f9269e44e6d34419cb92242ef11aee9351ac", "type": "zip", "shasum": "", "reference": "5210f9269e44e6d34419cb92242ef11aee9351ac"}, "time": "2020-08-10T07:24:51+00:00", "require": {"php": "^7.3 || ^8.0", "ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.7", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}}, {"version": "9.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "9e17fe39fcaf66375f3d9d2258b025c3f48e42ac"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/9e17fe39fcaf66375f3d9d2258b025c3f48e42ac", "type": "zip", "shasum": "", "reference": "9e17fe39fcaf66375f3d9d2258b025c3f48e42ac"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.1.1"}, "time": "2020-08-10T04:37:38+00:00"}, {"version": "9.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "0bc0140bb9bde4a34222e90a5802a88c6c596cd9"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/0bc0140bb9bde4a34222e90a5802a88c6c596cd9", "type": "zip", "shasum": "", "reference": "0bc0140bb9bde4a34222e90a5802a88c6c596cd9"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2020-08-10T04:19:29+00:00"}, {"version": "9.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "ee24e82baca11d7d6fb3513e127d6000f541cf90"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ee24e82baca11d7d6fb3513e127d6000f541cf90", "type": "zip", "shasum": "", "reference": "ee24e82baca11d7d6fb3513e127d6000f541cf90"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.0.0"}, "time": "2020-08-07T04:12:30+00:00", "extra": {"branch-alias": {"dev-master": "9.0-dev"}}}, {"version": "8.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "ca6647ffddd2add025ab3f21644a441d7c146cdc"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ca6647ffddd2add025ab3f21644a441d7c146cdc", "type": "zip", "shasum": "", "reference": "ca6647ffddd2add025ab3f21644a441d7c146cdc"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/8.0.2"}, "time": "2020-05-23T08:02:54+00:00", "extra": {"branch-alias": {"dev-master": "8.0-dev"}}, "require": {"php": "^7.3", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^3.0", "phpunit/php-token-stream": "^4.0", "phpunit/php-text-template": "^2.0", "sebastian/code-unit-reverse-lookup": "^2.0", "sebastian/environment": "^5.0", "sebastian/version": "^3.0", "theseer/tokenizer": "^1.1.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}}, {"version": "8.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "31e94ccc084025d6abee0585df533eb3a792b96a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/31e94ccc084025d6abee0585df533eb3a792b96a", "type": "zip", "shasum": "", "reference": "31e94ccc084025d6abee0585df533eb3a792b96a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/8.0.1"}, "funding": [], "time": "2020-02-19T13:41:19+00:00"}, {"version": "8.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "278b6e876467da2e6b5e5390a2310391618ebc10"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/278b6e876467da2e6b5e5390a2310391618ebc10", "type": "zip", "shasum": "", "reference": "278b6e876467da2e6b5e5390a2310391618ebc10"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/8.0.0"}, "time": "2020-02-07T06:03:55+00:00", "funding": "__unset"}, {"version": "7.0.17", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "40a4ed114a4aea5afd6df8d0f0c9cd3033097f66"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/40a4ed114a4aea5afd6df8d0f0c9cd3033097f66", "type": "zip", "shasum": "", "reference": "40a4ed114a4aea5afd6df8d0f0c9cd3033097f66"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.17"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:09:37+00:00", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "require": {"php": ">=7.2", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-token-stream": "^3.1.3 || ^4.0", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}, "require-dev": {"phpunit/phpunit": "^8.2.2"}, "suggest": {"ext-xdebug": "^2.7.2"}}, {"version": "7.0.16", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "486f046ff738fe76020eea245dbe6c67b100a9da"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/486f046ff738fe76020eea245dbe6c67b100a9da", "type": "zip", "shasum": "", "reference": "486f046ff738fe76020eea245dbe6c67b100a9da"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.16"}, "time": "2024-03-01T13:35:47+00:00"}, {"version": "7.0.15", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "819f92bba8b001d4363065928088de22f25a3a48"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/819f92bba8b001d4363065928088de22f25a3a48", "type": "zip", "shasum": "", "reference": "819f92bba8b001d4363065928088de22f25a3a48"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.15"}, "time": "2021-07-26T12:20:09+00:00"}, {"version": "7.0.14", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "bb7c9a210c72e4709cdde67f8b7362f672f2225c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/bb7c9a210c72e4709cdde67f8b7362f672f2225c", "type": "zip", "shasum": "", "reference": "bb7c9a210c72e4709cdde67f8b7362f672f2225c"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.14"}, "time": "2020-12-02T13:39:03+00:00", "require": {"php": ">=7.2", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-token-stream": "^3.1.1 || ^4.0", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}}, {"version": "7.0.13", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "ad0dcd7b184e76f7198a1fe07685bfbec3ae911a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ad0dcd7b184e76f7198a1fe07685bfbec3ae911a", "type": "zip", "shasum": "", "reference": "ad0dcd7b184e76f7198a1fe07685bfbec3ae911a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.13"}, "time": "2020-11-30T08:35:22+00:00", "require": {"php": ">=7.2", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-token-stream": "^3.1.1", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}}, {"version": "7.0.12", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "52f55786aa2e52c26cd9e2db20aff2981e0f7399"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/52f55786aa2e52c26cd9e2db20aff2981e0f7399", "type": "zip", "shasum": "", "reference": "52f55786aa2e52c26cd9e2db20aff2981e0f7399"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.12"}, "time": "2020-11-27T06:08:35+00:00", "require": {"php": "^7.2", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-token-stream": "^3.1.1", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}}, {"version": "7.0.11", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "8eb390a862ec0408fd8ec90fa939ca253ef14218"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/8eb390a862ec0408fd8ec90fa939ca253ef14218", "type": "zip", "shasum": "", "reference": "8eb390a862ec0408fd8ec90fa939ca253ef14218"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.11"}, "time": "2020-11-27T05:19:31+00:00"}, {"version": "7.0.10", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "f1884187926fbb755a9aaf0b3836ad3165b478bf"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/f1884187926fbb755a9aaf0b3836ad3165b478bf", "type": "zip", "shasum": "", "reference": "f1884187926fbb755a9aaf0b3836ad3165b478bf"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.10"}, "time": "2019-11-20T13:55:58+00:00", "funding": "__unset"}, {"version": "7.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "075572dd777ea8317196838f8b59b1de2a642abc"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/075572dd777ea8317196838f8b59b1de2a642abc", "type": "zip", "shasum": "", "reference": "075572dd777ea8317196838f8b59b1de2a642abc"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2019-11-20T08:52:29+00:00"}, {"version": "7.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "aa0d179a13284c7420fc281fc32750e6cc7c9e2f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/aa0d179a13284c7420fc281fc32750e6cc7c9e2f", "type": "zip", "shasum": "", "reference": "aa0d179a13284c7420fc281fc32750e6cc7c9e2f"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.8"}, "time": "2019-09-17T06:24:36+00:00"}, {"version": "7.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "7743bbcfff2a907e9ee4a25be13d0f8ec5e73800"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/7743bbcfff2a907e9ee4a25be13d0f8ec5e73800", "type": "zip", "shasum": "", "reference": "7743bbcfff2a907e9ee4a25be13d0f8ec5e73800"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2019-07-25T05:31:54+00:00", "require": {"php": "^7.2", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-token-stream": "^3.1.0", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}}, {"version": "7.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "d471d0d2b529a67c6a722dd446c4ec90881ac315"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/d471d0d2b529a67c6a722dd446c4ec90881ac315", "type": "zip", "shasum": "", "reference": "d471d0d2b529a67c6a722dd446c4ec90881ac315"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.6"}, "time": "2019-07-08T05:29:42+00:00", "require": {"php": "^7.2", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-token-stream": "^3.0.2", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}}, {"version": "7.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "aed67b57d459dcab93e84a5c9703d3deb5025dff"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/aed67b57d459dcab93e84a5c9703d3deb5025dff", "type": "zip", "shasum": "", "reference": "aed67b57d459dcab93e84a5c9703d3deb5025dff"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2019-06-06T12:28:18+00:00", "require": {"php": "^7.2", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-token-stream": "^3.0.1", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.1", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}, "require-dev": {"phpunit/phpunit": "^8.0"}, "suggest": {"ext-xdebug": "^2.6.1"}}, {"version": "7.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "6024c8a6cb962d496b7bd049ed8f48473824176d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/6024c8a6cb962d496b7bd049ed8f48473824176d", "type": "zip", "shasum": "", "reference": "6024c8a6cb962d496b7bd049ed8f48473824176d"}, "time": "2019-05-29T09:59:31+00:00"}, {"version": "7.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "0317a769a81845c390e19684d9ba25d7f6aa4707"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/0317a769a81845c390e19684d9ba25d7f6aa4707", "type": "zip", "shasum": "", "reference": "0317a769a81845c390e19684d9ba25d7f6aa4707"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.3"}, "time": "2019-02-26T07:38:26+00:00"}, {"version": "7.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "cfca9c5f7f2694ca0c7749ffb142927d9f05250f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/cfca9c5f7f2694ca0c7749ffb142927d9f05250f", "type": "zip", "shasum": "", "reference": "cfca9c5f7f2694ca0c7749ffb142927d9f05250f"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2019-02-15T13:40:27+00:00"}, {"version": "7.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "4832739a02c418397e404da6c3e4fe680b7a4de7"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/4832739a02c418397e404da6c3e4fe680b7a4de7", "type": "zip", "shasum": "", "reference": "4832739a02c418397e404da6c3e4fe680b7a4de7"}, "time": "2019-02-01T07:29:14+00:00"}, {"version": "7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "2b0142ca3407ecabecd1772b37184eb1e61b3cca"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/2b0142ca3407ecabecd1772b37184eb1e61b3cca", "type": "zip", "shasum": "", "reference": "2b0142ca3407ecabecd1772b37184eb1e61b3cca"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.0"}, "time": "2019-02-01T05:31:34+00:00"}, {"version": "6.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/807e6013b00af69b6c5d9ceb4282d0393dbb9d8d", "type": "zip", "shasum": "", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2018-10-31T16:06:48+00:00", "extra": {"branch-alias": {"dev-master": "6.1-dev"}}, "require": {"php": "^7.1", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^2.0", "phpunit/php-token-stream": "^3.0", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.1 || ^4.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "suggest": {"ext-xdebug": "^2.6.0"}}, {"version": "6.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "4d3ae9b21a7d7e440bd0cf65565533117976859f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/4d3ae9b21a7d7e440bd0cf65565533117976859f", "type": "zip", "shasum": "", "reference": "4d3ae9b21a7d7e440bd0cf65565533117976859f"}, "time": "2018-10-23T05:59:32+00:00"}, {"version": "6.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "f317d4eb2023a3d9c716b715244348dbc5b81a56"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/f317d4eb2023a3d9c716b715244348dbc5b81a56", "type": "zip", "shasum": "", "reference": "f317d4eb2023a3d9c716b715244348dbc5b81a56"}, "time": "2018-10-23T05:06:12+00:00", "require": {"php": "^7.1", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^2.0", "phpunit/php-token-stream": "^3.0", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}}, {"version": "6.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "b097681a19a48e52706f57e47a09594bac4f7cab"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/b097681a19a48e52706f57e47a09594bac4f7cab", "type": "zip", "shasum": "", "reference": "b097681a19a48e52706f57e47a09594bac4f7cab"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/6.1.1"}, "time": "2018-10-18T09:01:38+00:00", "require": {"php": "^7.1", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^2.0", "phpunit/php-token-stream": "^3.0", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.1 || ^4.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}}, {"version": "6.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "0685fb6a43aed1b2e09804d1aaf17144c82861f8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/0685fb6a43aed1b2e09804d1aaf17144c82861f8", "type": "zip", "shasum": "", "reference": "0685fb6a43aed1b2e09804d1aaf17144c82861f8"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2018-10-16T05:37:37+00:00", "require": {"php": "^7.1", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^2.0", "phpunit/php-token-stream": "^3.0", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.1", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}}, {"version": "6.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "848f78b3309780fef7ec8c4666b7ab4e6b09b22f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/848f78b3309780fef7ec8c4666b7ab4e6b09b22f", "type": "zip", "shasum": "", "reference": "848f78b3309780fef7ec8c4666b7ab4e6b09b22f"}, "time": "2018-10-04T03:41:23+00:00", "extra": {"branch-alias": {"dev-master": "6.0-dev"}}}, {"version": "6.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "865662550c384bc1db7e51d29aeda1c2c161d69a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/865662550c384bc1db7e51d29aeda1c2c161d69a", "type": "zip", "shasum": "", "reference": "865662550c384bc1db7e51d29aeda1c2c161d69a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/6.0.7"}, "time": "2018-06-01T07:51:50+00:00"}, {"version": "6.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "1661926cfa5be825d4f96f89dfac46dc4a19afa8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/1661926cfa5be825d4f96f89dfac46dc4a19afa8", "type": "zip", "shasum": "", "reference": "1661926cfa5be825d4f96f89dfac46dc4a19afa8"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2018-06-01T05:41:09+00:00"}, {"version": "6.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "4cab20a326d14de7575a8e235c70d879b569a57a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/4cab20a326d14de7575a8e235c70d879b569a57a", "type": "zip", "shasum": "", "reference": "4cab20a326d14de7575a8e235c70d879b569a57a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/6.0.5"}, "time": "2018-05-28T11:49:20+00:00", "require": {"php": "^7.1", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^1.4.2", "phpunit/php-token-stream": "^3.0", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.1", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}}, {"version": "6.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "52187754b0eed0b8159f62a6fa30073327e8c2ca"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/52187754b0eed0b8159f62a6fa30073327e8c2ca", "type": "zip", "shasum": "", "reference": "52187754b0eed0b8159f62a6fa30073327e8c2ca"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/6.0.4"}, "time": "2018-04-29T14:59:09+00:00"}, {"version": "6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "774a82c0c5da4c1c7701790c262035d235ab7856"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/774a82c0c5da4c1c7701790c262035d235ab7856", "type": "zip", "shasum": "", "reference": "774a82c0c5da4c1c7701790c262035d235ab7856"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2018-04-06T15:39:20+00:00"}, {"version": "6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "2334b69c07ca9fe3a8dbd1c91d298708b87ac3ad"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/2334b69c07ca9fe3a8dbd1c91d298708b87ac3ad", "type": "zip", "shasum": "", "reference": "2334b69c07ca9fe3a8dbd1c91d298708b87ac3ad"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/6.0.2"}, "time": "2018-04-06T14:14:47+00:00", "require": {"php": "^7.1", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^1.4.2", "phpunit/php-token-stream": "^3.0", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}}, {"version": "6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "f8ca4b604baf23dab89d87773c28cc07405189ba"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/f8ca4b604baf23dab89d87773c28cc07405189ba", "type": "zip", "shasum": "", "reference": "f8ca4b604baf23dab89d87773c28cc07405189ba"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/6.0.1"}, "time": "2018-02-02T07:01:41+00:00"}, {"version": "6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "d3e39edbd915a6ffaa7016f822043f6488741e75"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/d3e39edbd915a6ffaa7016f822043f6488741e75", "type": "zip", "shasum": "", "reference": "d3e39edbd915a6ffaa7016f822043f6488741e75"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2018-02-01T13:13:16+00:00"}, {"version": "5.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "c89677919c5dd6d3b3852f230a663118762218ac"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/c89677919c5dd6d3b3852f230a663118762218ac", "type": "zip", "shasum": "", "reference": "c89677919c5dd6d3b3852f230a663118762218ac"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/5.3"}, "time": "2018-04-06T15:36:58+00:00", "extra": {"branch-alias": {"dev-master": "5.3.x-dev"}}, "require": {"php": "^7.0", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^1.4.2", "phpunit/php-token-stream": "^2.0.1", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-xdebug": "^2.5.5"}}, {"version": "5.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "340d86b2c417f6966b8fa0aee339ba3f671ebab7"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/340d86b2c417f6966b8fa0aee339ba3f671ebab7", "type": "zip", "shasum": "", "reference": "340d86b2c417f6966b8fa0aee339ba3f671ebab7"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/5.3.1"}, "time": "2018-04-06T14:09:56+00:00"}, {"version": "5.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "661f34d0bd3f1a7225ef491a70a020ad23a057a1"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/661f34d0bd3f1a7225ef491a70a020ad23a057a1", "type": "zip", "shasum": "", "reference": "661f34d0bd3f1a7225ef491a70a020ad23a057a1"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/5.3.0"}, "time": "2017-12-06T09:29:45+00:00"}, {"version": "5.2.4", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "033ec97498cf530cc1be4199264cad568b19be26"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/033ec97498cf530cc1be4199264cad568b19be26", "type": "zip", "shasum": "", "reference": "033ec97498cf530cc1be4199264cad568b19be26"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2017-11-27T09:00:30+00:00", "extra": {"branch-alias": {"dev-master": "5.2.x-dev"}}, "require-dev": {"phpunit/phpunit": "^6.0", "ext-xdebug": "^2.5"}}, {"version": "5.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "8e1d2397d8adf59a3f12b2878a3aaa66d1ab189d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/8e1d2397d8adf59a3f12b2878a3aaa66d1ab189d", "type": "zip", "shasum": "", "reference": "8e1d2397d8adf59a3f12b2878a3aaa66d1ab189d"}, "time": "2017-11-03T13:47:33+00:00", "require": {"php": "^7.0", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^1.4.2", "phpunit/php-token-stream": "^2.0", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}}, {"version": "5.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "8ed1902a57849e117b5651fc1a5c48110946c06b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/8ed1902a57849e117b5651fc1a5c48110946c06b", "type": "zip", "shasum": "", "reference": "8ed1902a57849e117b5651fc1a5c48110946c06b"}, "time": "2017-08-03T12:40:43+00:00", "require": {"php": "^7.0", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^1.4.2", "phpunit/php-token-stream": "^1.4.11 || ^2.0", "phpunit/php-text-template": "^1.2.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}}, {"version": "5.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "dc421f9ca5082a0c0cb04afb171c765f79add85b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/dc421f9ca5082a0c0cb04afb171c765f79add85b", "type": "zip", "shasum": "", "reference": "dc421f9ca5082a0c0cb04afb171c765f79add85b"}, "time": "2017-04-21T08:03:57+00:00", "require": {"php": "^7.0", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^1.3", "phpunit/php-token-stream": "^1.4.11 || ^2.0", "phpunit/php-text-template": "^1.2", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/environment": "^3.0", "sebastian/version": "^2.0", "theseer/tokenizer": "^1.1"}, "suggest": {"ext-xdebug": "^2.5.3"}}, {"version": "5.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "e648abfd8ffb1d54ad549b027b75e376e9055d02"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/e648abfd8ffb1d54ad549b027b75e376e9055d02", "type": "zip", "shasum": "", "reference": "e648abfd8ffb1d54ad549b027b75e376e9055d02"}, "time": "2017-04-20T10:00:57+00:00", "require": {"php": "^7.0", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^1.3", "phpunit/php-token-stream": "^1.4.11 || ^2.0", "phpunit/php-text-template": "^1.2", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/environment": "^2.0", "sebastian/version": "^2.0", "theseer/tokenizer": "^1.1"}}, {"version": "5.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "bc433b7af27e0ab9b6b4c6d8ec918a493875f6bc"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/bc433b7af27e0ab9b6b4c6d8ec918a493875f6bc", "type": "zip", "shasum": "", "reference": "bc433b7af27e0ab9b6b4c6d8ec918a493875f6bc"}, "time": "2017-04-12T07:59:32+00:00", "extra": {"branch-alias": {"dev-master": "5.1.x-dev"}}, "require": {"php": "^7.0", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^1.3", "phpunit/php-token-stream": "^1.4.11 || ^2.0", "phpunit/php-text-template": "^1.2", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/environment": "^2.0", "sebastian/version": "^2.0"}, "suggest": {"ext-xdebug": "^2.5.1"}}, {"version": "5.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "cff36444733ac6d3f153866f55898373ca184610"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/cff36444733ac6d3f153866f55898373ca184610", "type": "zip", "shasum": "", "reference": "cff36444733ac6d3f153866f55898373ca184610"}, "time": "2017-04-07T04:39:58+00:00"}, {"version": "5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "37bbf491e6b585267ef1e0cbb0056f5c00ebf91c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/37bbf491e6b585267ef1e0cbb0056f5c00ebf91c", "type": "zip", "shasum": "", "reference": "37bbf491e6b585267ef1e0cbb0056f5c00ebf91c"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/5.0"}, "time": "2017-04-02T07:46:32+00:00", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}}, {"version": "5.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "4e99e1c4f9b05cbf4d6e84b100b3ff4107cf8cd1"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/4e99e1c4f9b05cbf4d6e84b100b3ff4107cf8cd1", "type": "zip", "shasum": "", "reference": "4e99e1c4f9b05cbf4d6e84b100b3ff4107cf8cd1"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2017-03-06T14:22:16+00:00"}, {"version": "5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "531553c4795a1df54114342d68ca337d5d81c8a0"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/531553c4795a1df54114342d68ca337d5d81c8a0", "type": "zip", "shasum": "", "reference": "531553c4795a1df54114342d68ca337d5d81c8a0"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/5.0.2"}, "time": "2017-03-01T09:14:18+00:00"}, {"version": "5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "3f10a2c8eed68b29cbbb54e29cc58cb31b077553"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/3f10a2c8eed68b29cbbb54e29cc58cb31b077553", "type": "zip", "shasum": "", "reference": "3f10a2c8eed68b29cbbb54e29cc58cb31b077553"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2017-02-23T07:27:58+00:00", "require": {"php": "^7.0", "phpunit/php-file-iterator": "^1.3", "phpunit/php-token-stream": "^1.4.2 || ^2.0", "phpunit/php-text-template": "^1.2", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/environment": "^2.0", "sebastian/version": "^2.0"}, "suggest": {"ext-dom": "*", "ext-xmlwriter": "*"}}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "e7d7a4acca58e45bdfd00221563d131cfb04ba96"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/e7d7a4acca58e45bdfd00221563d131cfb04ba96", "type": "zip", "shasum": "", "reference": "e7d7a4acca58e45bdfd00221563d131cfb04ba96"}, "time": "2017-02-02T10:35:41+00:00", "require": {"php": "^7.0", "phpunit/php-file-iterator": "^1.3", "phpunit/php-token-stream": "^1.4.2", "phpunit/php-text-template": "^1.2", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/environment": "^2.0", "sebastian/version": "^2.0"}}, {"version": "4.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "type": "zip", "shasum": "", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/4.0"}, "time": "2017-04-02T07:44:40+00:00", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "require": {"php": "^5.6 || ^7.0", "ext-dom": "*", "ext-xmlwriter": "*", "phpunit/php-file-iterator": "^1.3", "phpunit/php-token-stream": "^1.4.2 || ^2.0", "phpunit/php-text-template": "^1.2", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/environment": "^1.3.2 || ^2.0", "sebastian/version": "^1.0 || ^2.0"}, "require-dev": {"phpunit/phpunit": "^5.7", "ext-xdebug": "^2.1.4"}, "suggest": {"ext-xdebug": "^2.5.1"}}, {"version": "4.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "09e2277d14ea467e5a984010f501343ef29ffc69"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/09e2277d14ea467e5a984010f501343ef29ffc69", "type": "zip", "shasum": "", "reference": "09e2277d14ea467e5a984010f501343ef29ffc69"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/4.0.7"}, "time": "2017-03-01T09:12:17+00:00"}, {"version": "4.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "ca060f645beeddebedb1885c97bf163e93264c35"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ca060f645beeddebedb1885c97bf163e93264c35", "type": "zip", "shasum": "", "reference": "ca060f645beeddebedb1885c97bf163e93264c35"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/4.0"}, "time": "2017-02-23T07:38:02+00:00", "require": {"php": "^5.6 || ^7.0", "phpunit/php-file-iterator": "~1.3", "phpunit/php-token-stream": "^1.4.2 || ^2.0", "phpunit/php-text-template": "~1.2", "sebastian/code-unit-reverse-lookup": "~1.0", "sebastian/environment": "^1.3.2 || ^2.0", "sebastian/version": "~1.0|~2.0"}, "require-dev": {"phpunit/phpunit": "^5.4", "ext-xdebug": ">=2.1.4"}, "suggest": {"ext-dom": "*", "ext-xdebug": ">=2.4.0", "ext-xmlwriter": "*"}}, {"version": "4.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "c19cfc7cbb0e9338d8c469c7eedecc2a428b0971"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/c19cfc7cbb0e9338d8c469c7eedecc2a428b0971", "type": "zip", "shasum": "", "reference": "c19cfc7cbb0e9338d8c469c7eedecc2a428b0971"}, "time": "2017-01-20T15:06:43+00:00", "require": {"php": "^5.6 || ^7.0", "phpunit/php-file-iterator": "~1.3", "phpunit/php-token-stream": "^1.4.2", "phpunit/php-text-template": "~1.2", "sebastian/code-unit-reverse-lookup": "~1.0", "sebastian/environment": "^1.3.2 || ^2.0", "sebastian/version": "~1.0|~2.0"}}, {"version": "4.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "c14196e64a78570034afd0b7a9f3757ba71c2a0a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/c14196e64a78570034afd0b7a9f3757ba71c2a0a", "type": "zip", "shasum": "", "reference": "c14196e64a78570034afd0b7a9f3757ba71c2a0a"}, "time": "2016-12-20T15:22:42+00:00"}, {"version": "4.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "903fd6318d0a90b4770a009ff73e4a4e9c437929"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/903fd6318d0a90b4770a009ff73e4a4e9c437929", "type": "zip", "shasum": "", "reference": "903fd6318d0a90b4770a009ff73e4a4e9c437929"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2016-11-28T16:00:31+00:00"}, {"version": "4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "6cba06ff75a1a63a71033e1a01b89056f3af1e8d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/6cba06ff75a1a63a71033e1a01b89056f3af1e8d", "type": "zip", "shasum": "", "reference": "6cba06ff75a1a63a71033e1a01b89056f3af1e8d"}, "time": "2016-11-01T05:06:24+00:00"}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "5f3f7e736d6319d5f1fc402aff8b026da26709a3"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/5f3f7e736d6319d5f1fc402aff8b026da26709a3", "type": "zip", "shasum": "", "reference": "5f3f7e736d6319d5f1fc402aff8b026da26709a3"}, "time": "2016-07-26T14:39:29+00:00"}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "900370c81280cc0d942ffbc5912d80464eaee7e9"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/900370c81280cc0d942ffbc5912d80464eaee7e9", "type": "zip", "shasum": "", "reference": "900370c81280cc0d942ffbc5912d80464eaee7e9"}, "time": "2016-06-03T05:03:56+00:00", "require": {"php": "^5.6 || ^7.0", "phpunit/php-file-iterator": "~1.3", "phpunit/php-token-stream": "^1.4.2", "phpunit/php-text-template": "~1.2", "sebastian/code-unit-reverse-lookup": "~1.0", "sebastian/environment": "^1.3.2", "sebastian/version": "~1.0|~2.0"}}, {"version": "3.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "44cd8e3930e431658d1a5de7d282d5cb37837fd5"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/44cd8e3930e431658d1a5de7d282d5cb37837fd5", "type": "zip", "shasum": "", "reference": "44cd8e3930e431658d1a5de7d282d5cb37837fd5"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/3.3"}, "time": "2016-05-27T16:24:29+00:00", "extra": {"branch-alias": {"dev-master": "3.3.x-dev"}}, "require-dev": {"phpunit/phpunit": "~5", "ext-xdebug": ">=2.1.4"}}, {"version": "3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "4862dac433cbdb4438b593fa7f79e07559ba157e"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/4862dac433cbdb4438b593fa7f79e07559ba157e", "type": "zip", "shasum": "", "reference": "4862dac433cbdb4438b593fa7f79e07559ba157e"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/3.3.2"}, "time": "2016-05-25T06:48:21+00:00"}, {"version": "3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "2431befdd451fac43fbcde94d1a92fb3b8b68f86"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/2431befdd451fac43fbcde94d1a92fb3b8b68f86", "type": "zip", "shasum": "", "reference": "2431befdd451fac43fbcde94d1a92fb3b8b68f86"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/3.3"}, "time": "2016-04-08T08:14:53+00:00"}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "fe33716763b604ade4cb442c0794f5bd5ad73004"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/fe33716763b604ade4cb442c0794f5bd5ad73004", "type": "zip", "shasum": "", "reference": "fe33716763b604ade4cb442c0794f5bd5ad73004"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/3.3.0"}, "time": "2016-03-03T08:49:08+00:00", "suggest": {"ext-dom": "*", "ext-xdebug": ">=2.2.1", "ext-xmlwriter": "*"}}, {"version": "3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "a58f95ae76fe201b571fad3e8370a50c4368678c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/a58f95ae76fe201b571fad3e8370a50c4368678c", "type": "zip", "shasum": "", "reference": "a58f95ae76fe201b571fad3e8370a50c4368678c"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/3.2.1"}, "time": "2016-02-18T07:31:12+00:00", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "85f5db2d0a0da79ad6a256eb54148ba383059ad9"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/85f5db2d0a0da79ad6a256eb54148ba383059ad9", "type": "zip", "shasum": "", "reference": "85f5db2d0a0da79ad6a256eb54148ba383059ad9"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/3.2.0"}, "time": "2016-02-13T06:47:56+00:00", "require": {"php": ">=5.6", "phpunit/php-file-iterator": "~1.3", "phpunit/php-token-stream": "~1.3", "phpunit/php-text-template": "~1.2", "sebastian/code-unit-reverse-lookup": "~1.0", "sebastian/environment": "^1.3.2", "sebastian/version": "~1.0|~2.0"}}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "92f5c61b5c64159faec5298325ffab0c7e59dcc8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/92f5c61b5c64159faec5298325ffab0c7e59dcc8", "type": "zip", "shasum": "", "reference": "92f5c61b5c64159faec5298325ffab0c7e59dcc8"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2016-02-04T13:05:19+00:00", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "require": {"php": ">=5.6", "phpunit/php-file-iterator": "~1.3", "phpunit/php-token-stream": "~1.3", "phpunit/php-text-template": "~1.2", "sebastian/environment": "^1.3.2", "sebastian/version": "~1.0|~2.0"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "64d40a593fc31a8abf4ce3d200147ddf8ca64e52"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/64d40a593fc31a8abf4ce3d200147ddf8ca64e52", "type": "zip", "shasum": "", "reference": "64d40a593fc31a8abf4ce3d200147ddf8ca64e52"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/3.1.0"}, "time": "2016-01-11T10:05:34+00:00", "require": {"php": ">=5.6", "phpunit/php-file-iterator": "~1.3", "phpunit/php-token-stream": "~1.3", "phpunit/php-text-template": "~1.2", "sebastian/environment": "^1.3.2", "sebastian/version": "~1.0"}}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "f7bb5cddf4ffe113eeb737b05241adb947b43f9d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/f7bb5cddf4ffe113eeb737b05241adb947b43f9d", "type": "zip", "shasum": "", "reference": "f7bb5cddf4ffe113eeb737b05241adb947b43f9d"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/3.0.2"}, "time": "2015-11-12T21:08:20+00:00", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "c84f05be256cd7c9d2340b26f7995b4afbf8787b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/c84f05be256cd7c9d2340b26f7995b4afbf8787b", "type": "zip", "shasum": "", "reference": "c84f05be256cd7c9d2340b26f7995b4afbf8787b"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2015-10-06T15:51:05+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "86e0dafb0e6c5165cce7b3b6892807d07504baa0"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/86e0dafb0e6c5165cce7b3b6892807d07504baa0", "type": "zip", "shasum": "", "reference": "86e0dafb0e6c5165cce7b3b6892807d07504baa0"}, "time": "2015-10-01T06:48:20+00:00"}, {"version": "2.2.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "eabf68b476ac7d0f73793aada060f1c1a9bf8979"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/eabf68b476ac7d0f73793aada060f1c1a9bf8979", "type": "zip", "shasum": "", "reference": "eabf68b476ac7d0f73793aada060f1c1a9bf8979"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.2"}, "time": "2015-10-06T15:47:00+00:00", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3", "phpunit/php-token-stream": "~1.3", "phpunit/php-text-template": "~1.2", "sebastian/environment": "^1.3.2", "sebastian/version": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4", "ext-xdebug": ">=2.1.4"}}, {"version": "2.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "ef1ca6835468857944d5c3b48fa503d5554cff2f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ef1ca6835468857944d5c3b48fa503d5554cff2f", "type": "zip", "shasum": "", "reference": "ef1ca6835468857944d5c3b48fa503d5554cff2f"}, "time": "2015-09-14T06:51:16+00:00"}, {"version": "2.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "2d7c03c0e4e080901b8f33b2897b0577be18a13c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/2d7c03c0e4e080901b8f33b2897b0577be18a13c", "type": "zip", "shasum": "", "reference": "2d7c03c0e4e080901b8f33b2897b0577be18a13c"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.2.2"}, "time": "2015-08-04T03:42:39+00:00"}, {"version": "2.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "6526d9bdb56e2af1e8950114d42391044d18cfa7"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/6526d9bdb56e2af1e8950114d42391044d18cfa7", "type": "zip", "shasum": "", "reference": "6526d9bdb56e2af1e8950114d42391044d18cfa7"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.2.1"}, "time": "2015-08-02T04:45:08+00:00", "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3", "phpunit/php-token-stream": "~1.3", "phpunit/php-text-template": "~1.2", "sebastian/environment": "~1.3.1", "sebastian/version": "~1.0"}}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "e6577d90f61a9adbe94544a6e9a7ca18b5fd9c8f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/e6577d90f61a9adbe94544a6e9a7ca18b5fd9c8f", "type": "zip", "shasum": "", "reference": "e6577d90f61a9adbe94544a6e9a7ca18b5fd9c8f"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.2.0"}, "time": "2015-08-01T05:09:57+00:00", "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3", "phpunit/php-token-stream": "~1.3", "phpunit/php-text-template": "~1.2", "sebastian/environment": "~1.3", "sebastian/version": "~1.0"}}, {"version": "2.1.9", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "5bd48b86cd282da411bb80baac1398ce3fefac41"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/5bd48b86cd282da411bb80baac1398ce3fefac41", "type": "zip", "shasum": "", "reference": "5bd48b86cd282da411bb80baac1398ce3fefac41"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.1.9"}, "time": "2015-07-26T12:54:47+00:00", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3", "phpunit/php-token-stream": "~1.3", "phpunit/php-text-template": "~1.2", "sebastian/environment": "~1.0", "sebastian/version": "~1.0"}}, {"version": "2.1.8", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "6044546998c7627ab997501a3d0db972b3db9790"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/6044546998c7627ab997501a3d0db972b3db9790", "type": "zip", "shasum": "", "reference": "6044546998c7627ab997501a3d0db972b3db9790"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.1.8"}, "time": "2015-07-13T11:25:58+00:00"}, {"version": "2.1.7", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "07e27765596d72c378a6103e80da5d84e802f1e4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/07e27765596d72c378a6103e80da5d84e802f1e4", "type": "zip", "shasum": "", "reference": "07e27765596d72c378a6103e80da5d84e802f1e4"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.1.7"}, "time": "2015-06-30T06:52:35+00:00"}, {"version": "2.1.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "631e365cf26bb2c078683e8d9bcf8bc631ac4d44"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/631e365cf26bb2c078683e8d9bcf8bc631ac4d44", "type": "zip", "shasum": "", "reference": "631e365cf26bb2c078683e8d9bcf8bc631ac4d44"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.1.6"}, "time": "2015-06-19T07:11:55+00:00"}, {"version": "2.1.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "be2286cb8c7e1773eded49d9719219e6f74f9e3e"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/be2286cb8c7e1773eded49d9719219e6f74f9e3e", "type": "zip", "shasum": "", "reference": "be2286cb8c7e1773eded49d9719219e6f74f9e3e"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.1.5"}, "time": "2015-06-09T13:05:42+00:00"}, {"version": "2.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "3703c4bb67c8700957dd41c843254658539d091d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/3703c4bb67c8700957dd41c843254658539d091d", "type": "zip", "shasum": "", "reference": "3703c4bb67c8700957dd41c843254658539d091d"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.1.4"}, "time": "2015-06-06T08:33:23+00:00"}, {"version": "2.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "28a6b34e91d789b2608072ab3c82eaae7cdb973c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/28a6b34e91d789b2608072ab3c82eaae7cdb973c", "type": "zip", "shasum": "", "reference": "28a6b34e91d789b2608072ab3c82eaae7cdb973c"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.1.3"}, "time": "2015-06-03T07:01:01+00:00"}, {"version": "2.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "6b7d2094ca2a685a2cad846cb7cd7a30e8b9470f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/6b7d2094ca2a685a2cad846cb7cd7a30e8b9470f", "type": "zip", "shasum": "", "reference": "6b7d2094ca2a685a2cad846cb7cd7a30e8b9470f"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.1.2"}, "time": "2015-06-01T07:35:26+00:00"}, {"version": "2.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "60991776b3994cd8297b861e8ddc7f9c9500dedc"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/60991776b3994cd8297b861e8ddc7f9c9500dedc", "type": "zip", "shasum": "", "reference": "60991776b3994cd8297b861e8ddc7f9c9500dedc"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.1.1"}, "time": "2015-05-31T03:14:06+00:00"}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "ba315f46873fd6e86fdb98685a1a900e7379c886"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ba315f46873fd6e86fdb98685a1a900e7379c886", "type": "zip", "shasum": "", "reference": "ba315f46873fd6e86fdb98685a1a900e7379c886"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.1.0"}, "time": "2015-05-30T12:58:40+00:00"}, {"version": "2.0.17", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "c4e8e7725e351184a76544634855b8a9c405a6e3"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/c4e8e7725e351184a76544634855b8a9c405a6e3", "type": "zip", "shasum": "", "reference": "c4e8e7725e351184a76544634855b8a9c405a6e3"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0"}, "time": "2015-05-25T05:11:59+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}}, {"version": "2.0.16", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "934fd03eb6840508231a7f73eb8940cf32c3b66c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/934fd03eb6840508231a7f73eb8940cf32c3b66c", "type": "zip", "shasum": "", "reference": "934fd03eb6840508231a7f73eb8940cf32c3b66c"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.16"}, "time": "2015-04-11T04:35:00+00:00"}, {"version": "2.0.15", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "34cc484af1ca149188d0d9e91412191e398e0b67"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/34cc484af1ca149188d0d9e91412191e398e0b67", "type": "zip", "shasum": "", "reference": "34cc484af1ca149188d0d9e91412191e398e0b67"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.15"}, "time": "2015-01-24T10:06:35+00:00"}, {"version": "2.0.14", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "ca158276c1200cc27f5409a5e338486bc0b4fc94"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ca158276c1200cc27f5409a5e338486bc0b4fc94", "type": "zip", "shasum": "", "reference": "ca158276c1200cc27f5409a5e338486bc0b4fc94"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.14"}, "time": "2014-12-26T13:28:33+00:00", "include-path": [""], "require-dev": {"phpunit/phpunit": "~4.1", "ext-xdebug": ">=2.1.4"}}, {"version": "2.0.13", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "0e7d2eec5554f869fa7a4ec2d21e4b37af943ea5"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/0e7d2eec5554f869fa7a4ec2d21e4b37af943ea5", "type": "zip", "shasum": "", "reference": "0e7d2eec5554f869fa7a4ec2d21e4b37af943ea5"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.13"}, "time": "2014-12-03T06:41:44+00:00"}, {"version": "2.0.12", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "7ce9da20f96964bb7a4033f53834df13328dbeab"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/7ce9da20f96964bb7a4033f53834df13328dbeab", "type": "zip", "shasum": "", "reference": "7ce9da20f96964bb7a4033f53834df13328dbeab"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.12"}, "time": "2014-12-02T13:17:01+00:00"}, {"version": "2.0.11", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "53603b3c995f5aab6b59c8e08c3a663d2cc810b7"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/53603b3c995f5aab6b59c8e08c3a663d2cc810b7", "type": "zip", "shasum": "", "reference": "53603b3c995f5aab6b59c8e08c3a663d2cc810b7"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.11"}, "time": "2014-08-31T06:33:04+00:00"}, {"version": "2.0.10", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "6d196af48e8c100a3ae881940123e693da5a9217"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/6d196af48e8c100a3ae881940123e693da5a9217", "type": "zip", "shasum": "", "reference": "6d196af48e8c100a3ae881940123e693da5a9217"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.10"}, "time": "2014-08-06T06:39:42+00:00", "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3.1", "phpunit/php-token-stream": "~1.2.2", "phpunit/php-text-template": "~1.2.0", "sebastian/environment": "~1.0.0", "sebastian/version": "~1.0.3"}, "require-dev": {"phpunit/phpunit": "~4.0.14", "ext-xdebug": ">=2.1.4"}}, {"version": "2.0.9", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "ed8ac99ce38c3fd134128c898f7ca74665abef7f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ed8ac99ce38c3fd134128c898f7ca74665abef7f", "type": "zip", "shasum": "", "reference": "ed8ac99ce38c3fd134128c898f7ca74665abef7f"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0"}, "time": "2014-06-29T08:14:40+00:00"}, {"version": "2.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "58401826c8cfc8fd689b60026e91c337df374bca"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/58401826c8cfc8fd689b60026e91c337df374bca", "type": "zip", "shasum": "", "reference": "58401826c8cfc8fd689b60026e91c337df374bca"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.8"}, "time": "2014-05-26T14:55:24+00:00"}, {"version": "2.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "3b62da716f1d106f6257353f1cbc63e24508b598"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/3b62da716f1d106f6257353f1cbc63e24508b598", "type": "zip", "shasum": "", "reference": "3b62da716f1d106f6257353f1cbc63e24508b598"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0"}, "time": "2014-05-15T04:11:01+00:00"}, {"version": "2.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "bccecf50645068b44f49a84009e2a0499a500b99"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/bccecf50645068b44f49a84009e2a0499a500b99", "type": "zip", "shasum": "", "reference": "bccecf50645068b44f49a84009e2a0499a500b99"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.6"}, "time": "2014-04-30T09:01:21+00:00"}, {"version": "2.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "3dcca2120451b98a98fe60221ca279a184ee64db"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/3dcca2120451b98a98fe60221ca279a184ee64db", "type": "zip", "shasum": "", "reference": "3dcca2120451b98a98fe60221ca279a184ee64db"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.5"}, "time": "2014-03-28T10:54:55+00:00", "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3.1", "phpunit/php-token-stream": "~1.2.2", "phpunit/php-text-template": "~1.2", "sebastian/environment": "~1.0", "sebastian/version": "~1.0.3"}, "require-dev": {"phpunit/phpunit": ">=4.0.0,<4.1.0", "ext-xdebug": ">=2.1.4"}, "suggest": {"ext-dom": "*", "ext-xdebug": ">=2.2.1"}}, {"version": "2.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "a05618d03c60eb07b37432583aabf80ae5f5f447"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/a05618d03c60eb07b37432583aabf80ae5f5f447", "type": "zip", "shasum": "", "reference": "a05618d03c60eb07b37432583aabf80ae5f5f447"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.4"}, "time": "2014-03-20T16:12:25+00:00"}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "0b1ff895907bf398ba88244f82b27887427f7d06"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/0b1ff895907bf398ba88244f82b27887427f7d06", "type": "zip", "shasum": "", "reference": "0b1ff895907bf398ba88244f82b27887427f7d06"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.3"}, "time": "2014-03-20T15:41:23+00:00", "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3.1", "phpunit/php-token-stream": "~1.1.3", "phpunit/php-text-template": "~1.2", "sebastian/environment": "~1.0", "sebastian/version": "~1.0.3"}}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "2c20ba4872d09d1d3d7ceda5a0c5f82d9a8ff31f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/2c20ba4872d09d1d3d7ceda5a0c5f82d9a8ff31f", "type": "zip", "shasum": "", "reference": "2c20ba4872d09d1d3d7ceda5a0c5f82d9a8ff31f"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.2"}, "time": "2014-03-17T10:25:49+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "23e6ac9513df2af67f9f713347f3e4bf4b59784c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/23e6ac9513df2af67f9f713347f3e4bf4b59784c", "type": "zip", "shasum": "", "reference": "23e6ac9513df2af67f9f713347f3e4bf4b59784c"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.1"}, "time": "2014-03-07T16:03:14+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "cbdd64d012c9b8c2e30850abd125001ed5db1eed"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/cbdd64d012c9b8c2e30850abd125001ed5db1eed", "type": "zip", "shasum": "", "reference": "cbdd64d012c9b8c2e30850abd125001ed5db1eed"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/2.0.0"}, "time": "2014-03-07T06:40:32+00:00", "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3.1", "phpunit/php-token-stream": "~1.1.3", "phpunit/php-text-template": "~1.2", "sebastian/environment": "~1.0", "sebastian/version": "~1.0"}}, {"version": "1.2.18", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "fe2466802556d3fe4e4d1d58ffd3ccfd0a19be0b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/fe2466802556d3fe4e4d1d58ffd3ccfd0a19be0b", "type": "zip", "shasum": "", "reference": "fe2466802556d3fe4e4d1d58ffd3ccfd0a19be0b"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.18"}, "time": "2014-09-02T10:13:14+00:00", "autoload": {"classmap": ["PHP/"]}, "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": ">=1.3.0@stable", "phpunit/php-token-stream": ">=1.1.3,<1.3.0", "phpunit/php-text-template": ">=1.2.0@stable"}, "require-dev": {"phpunit/phpunit": "3.7.*@dev"}, "suggest": {"ext-dom": "*", "ext-xdebug": ">=2.0.5"}}, {"version": "1.2.17", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "6ef2bf3a1c47eca07ea95f0d8a902a6340390b34"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/6ef2bf3a1c47eca07ea95f0d8a902a6340390b34", "type": "zip", "shasum": "", "reference": "6ef2bf3a1c47eca07ea95f0d8a902a6340390b34"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.17"}, "time": "2014-03-28T10:53:45+00:00", "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": ">=1.3.0@stable", "phpunit/php-token-stream": ">=1.1.3@stable", "phpunit/php-text-template": ">=1.2.0@stable"}}, {"version": "1.2.16", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "69e55e68481cf708a6db43aff0b504e31402fe27"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/69e55e68481cf708a6db43aff0b504e31402fe27", "type": "zip", "shasum": "", "reference": "69e55e68481cf708a6db43aff0b504e31402fe27"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.16"}, "time": "2014-02-25T03:34:05+00:00"}, {"version": "1.2.15", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "6ba4ed2895d538a039d5d5866edc4ec0424c7852"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/6ba4ed2895d538a039d5d5866edc4ec0424c7852", "type": "zip", "shasum": "", "reference": "6ba4ed2895d538a039d5d5866edc4ec0424c7852"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.15"}, "time": "2014-02-03T07:44:47+00:00"}, {"version": "1.2.14", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "d4b3bc1c4b905cd0b3b356e8608857f26a041c1b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/d4b3bc1c4b905cd0b3b356e8608857f26a041c1b", "type": "zip", "shasum": "", "reference": "d4b3bc1c4b905cd0b3b356e8608857f26a041c1b"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.14"}, "time": "2014-01-31T08:51:59+00:00"}, {"version": "1.2.13", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "466e7cd2554b4e264c9e3f31216d25ac0e5f3d94"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/466e7cd2554b4e264c9e3f31216d25ac0e5f3d94", "type": "zip", "shasum": "", "reference": "466e7cd2554b4e264c9e3f31216d25ac0e5f3d94"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.13"}, "time": "2013-09-10T08:14:32+00:00", "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": ">=1.3.0@stable", "phpunit/php-token-stream": ">=1.1.3@stable", "phpunit/php-text-template": ">=1.1.1@stable"}}, {"version": "1.2.12", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "0e9958c459d675fb497d8dc5001c91d335734e48"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/0e9958c459d675fb497d8dc5001c91d335734e48", "type": "zip", "shasum": "", "reference": "0e9958c459d675fb497d8dc5001c91d335734e48"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2"}, "time": "2013-07-06T06:26:16+00:00"}, {"version": "1.2.11", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "630f56d5b7e36395dbeec4f37cb5ec1fcc19c1ef"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/630f56d5b7e36395dbeec4f37cb5ec1fcc19c1ef", "type": "zip", "shasum": "", "reference": "630f56d5b7e36395dbeec4f37cb5ec1fcc19c1ef"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.11"}, "time": "2013-05-23T18:23:24+00:00", "require-dev": {"phpunit/phpunit": "3.7.*"}, "extra": "__unset"}, {"version": "1.2.10", "version_normalized": "********", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "740558a9e2bb99bf006ed4f41533e0600def9bd3"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/740558a9e2bb99bf006ed4f41533e0600def9bd3", "type": "zip", "shasum": "", "reference": "740558a9e2bb99bf006ed4f41533e0600def9bd3"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.10"}, "time": "2013-05-13T05:49:10+00:00", "require-dev": "__unset"}, {"version": "1.2.9", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "3888fcba646930da78c8ff4dd3555f4da4caa0e2"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/3888fcba646930da78c8ff4dd3555f4da4caa0e2", "type": "zip", "shasum": "", "reference": "3888fcba646930da78c8ff4dd3555f4da4caa0e2"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.9"}, "time": "2013-02-26T18:55:56+00:00"}, {"version": "1.2.8", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "985dd4b2a45a74b4db4d9726b8a504fe83bb8db8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/985dd4b2a45a74b4db4d9726b8a504fe83bb8db8", "type": "zip", "shasum": "", "reference": "985dd4b2a45a74b4db4d9726b8a504fe83bb8db8"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.8"}, "time": "2013-02-14T08:01:51+00:00"}, {"version": "1.2.7", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "348b5d2ab7d3946a932c72a8165e64dde3cf4ec3"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/348b5d2ab7d3946a932c72a8165e64dde3cf4ec3", "type": "zip", "shasum": "", "reference": "348b5d2ab7d3946a932c72a8165e64dde3cf4ec3"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.7"}, "time": "2012-12-02T14:54:55+00:00"}, {"version": "1.2.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "0747b521b2b9779febad0ba7386e89746eb7fe7b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/0747b521b2b9779febad0ba7386e89746eb7fe7b", "type": "zip", "shasum": "", "reference": "0747b521b2b9779febad0ba7386e89746eb7fe7b"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.6"}, "time": "2012-10-17T05:34:13+00:00"}, {"homepage": "http://www.phpunit.de/", "version": "1.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "375c6260233881e5f4ef0f02e2a79f265cbf7ea8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/375c6260233881e5f4ef0f02e2a79f265cbf7ea8", "type": "zip", "shasum": "", "reference": "375c6260233881e5f4ef0f02e2a79f265cbf7ea8"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.3"}, "time": "2012-10-05T00:00:00+00:00", "suggest": {"ext-dom": "*", "ext-reflection": "*", "ext-spl": "*", "ext-xdebug": ">=2.0.5"}}, {"version": "1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "4b64fdf67bae61872e2484c22119bad55fbd64cf"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/4b64fdf67bae61872e2484c22119bad55fbd64cf", "type": "zip", "shasum": "", "reference": "4b64fdf67bae61872e2484c22119bad55fbd64cf"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.2"}, "time": "2012-09-20T00:00:00+00:00", "autoload": {"files": ["PHP/CodeCoverage/Autoload.php"]}}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "44994dbb7a19b69d44068db7d9bc93d296f97011"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/44994dbb7a19b69d44068db7d9bc93d296f97011", "type": "zip", "shasum": "", "reference": "44994dbb7a19b69d44068db7d9bc93d296f97011"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.1"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "type": "git", "reference": "21d0f05b8127057159eec4be79f8f8a92bc90957"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/21d0f05b8127057159eec4be79f8f8a92bc90957", "type": "zip", "shasum": "", "reference": "21d0f05b8127057159eec4be79f8f8a92bc90957"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/1.2.0"}, "time": "2012-09-19T00:00:00+00:00"}]}, "security-advisories": [], "last-modified": "Tue, 02 Sep 2025 05:23:29 GMT"}