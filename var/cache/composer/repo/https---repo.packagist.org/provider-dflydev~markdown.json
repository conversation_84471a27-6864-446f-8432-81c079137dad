{"minified": "composer/2.0", "packages": {"dflydev/markdown": [{"name": "dflydev/markdown", "description": "PHP Markdown & Extra", "keywords": ["markdown"], "homepage": "http://github.com/dflydev/dflydev-markdown", "version": "v1.0.3", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "homepage": "http://michelf.com"}, {"name": "<PERSON>", "homepage": "http://daringfireball.net"}], "source": {"url": "https://github.com/dflydev/dflydev-markdown.git", "type": "git", "reference": "6baed9b50f29c980795b6656d43722aadb126f7e"}, "dist": {"url": "https://api.github.com/repos/dflydev/dflydev-markdown/zipball/6baed9b50f29c980795b6656d43722aadb126f7e", "type": "zip", "shasum": "", "reference": "6baed9b50f29c980795b6656d43722aadb126f7e"}, "type": "library", "time": "2013-09-23T12:00:18+00:00", "autoload": {"psr-0": {"dflydev\\markdown": "src"}}, "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require": {"php": ">=5.3"}, "abandoned": "michelf/php-markdown", "support": {"issues": "https://github.com/dflydev/dflydev-markdown/issues", "source": "https://github.com/dflydev/dflydev-markdown/tree/master"}}, {"version": "v1.0.2", "version_normalized": "*******", "license": ["New BSD License"], "source": {"url": "https://github.com/dflydev/dflydev-markdown.git", "type": "git", "reference": "2a1b3516bd5af6e722b40ae9e1fccd03e1772060"}, "dist": {"url": "https://api.github.com/repos/dflydev/dflydev-markdown/zipball/2a1b3516bd5af6e722b40ae9e1fccd03e1772060", "type": "zip", "shasum": "", "reference": "2a1b3516bd5af6e722b40ae9e1fccd03e1772060"}, "time": "2012-01-16T03:36:37+00:00", "support": {"issues": "https://github.com/dflydev/dflydev-markdown/issues", "source": "https://github.com/dflydev/dflydev-markdown/tree/v1.0.2"}, "extra": "__unset"}, {"version": "v1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/dflydev/dflydev-markdown.git", "type": "git", "reference": "fdf608822e2b07f236e0fcc7f3bb5863ac606724"}, "dist": {"url": "https://api.github.com/repos/dflydev/dflydev-markdown/zipball/fdf608822e2b07f236e0fcc7f3bb5863ac606724", "type": "zip", "shasum": "", "reference": "fdf608822e2b07f236e0fcc7f3bb5863ac606724"}, "time": "2012-01-07T03:18:18+00:00", "support": {"issues": "https://github.com/dflydev/dflydev-markdown/issues", "source": "https://github.com/dflydev/dflydev-markdown/tree/v1.0.1"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/dflydev/dflydev-markdown.git", "type": "git", "reference": "76501a808522dbe40a5a71d272bd08d54cbae03d"}, "dist": {"url": "https://api.github.com/repos/dflydev/dflydev-markdown/zipball/76501a808522dbe40a5a71d272bd08d54cbae03d", "type": "zip", "shasum": "", "reference": "76501a808522dbe40a5a71d272bd08d54cbae03d"}, "time": "2012-01-02T23:11:32+00:00", "support": {"issues": "https://github.com/dflydev/dflydev-markdown/issues", "source": "https://github.com/dflydev/dflydev-markdown/tree/v1.0.0"}}]}, "security-advisories": [], "last-modified": "Wed, 06 Sep 2023 12:10:44 GMT"}