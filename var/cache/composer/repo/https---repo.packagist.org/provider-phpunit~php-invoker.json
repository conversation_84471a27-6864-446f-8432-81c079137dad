{"minified": "composer/2.0", "packages": {"phpunit/php-invoker": [{"name": "phpunit/php-invoker", "description": "Invoke callables with a timeout", "keywords": ["process"], "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "version": "6.0.0", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "12b54e689b07a25a9b41e57736dfab6ec9ae5406"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/12b54e689b07a25a9b41e57736dfab6ec9ae5406", "type": "zip", "shasum": "", "reference": "12b54e689b07a25a9b41e57736dfab6ec9ae5406"}, "type": "library", "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:58:58+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "require": {"php": ">=8.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^12.0"}, "suggest": {"ext-pcntl": "*"}}, {"version": "5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "c1ca3814734c07492b3d4c5f794f4b0995333da2"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/c1ca3814734c07492b3d4c5f794f4b0995333da2", "type": "zip", "shasum": "", "reference": "c1ca3814734c07492b3d4c5f794f4b0995333da2"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/5.0.1"}, "time": "2024-07-03T05:07:44+00:00", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "require": {"php": ">=8.2"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^11.0"}}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "5d8d9355a16d8cc5a1305b0a85342cfa420612be"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/5d8d9355a16d8cc5a1305b0a85342cfa420612be", "type": "zip", "shasum": "", "reference": "5d8d9355a16d8cc5a1305b0a85342cfa420612be"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/5.0.0"}, "time": "2024-02-02T06:05:50+00:00"}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "type": "zip", "shasum": "", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/4.0.0"}, "time": "2023-02-03T06:56:09+00:00", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "require": {"php": ">=8.1"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^10.0"}}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "type": "zip", "shasum": "", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.1.1"}, "time": "2020-09-28T05:58:55+00:00", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "7a85b66acc48cacffdf87dadd3694e7123674298"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/7a85b66acc48cacffdf87dadd3694e7123674298", "type": "zip", "shasum": "", "reference": "7a85b66acc48cacffdf87dadd3694e7123674298"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.1.0"}, "time": "2020-08-06T07:04:15+00:00", "require": {"php": "^7.3 || ^8.0"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.0"}}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "f6eedfed1085dd1f4c599629459a0277d25f9a66"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/f6eedfed1085dd1f4c599629459a0277d25f9a66", "type": "zip", "shasum": "", "reference": "f6eedfed1085dd1f4c599629459a0277d25f9a66"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/master"}, "time": "2020-06-26T11:53:53+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "62f696ad0d140e0e513e69eaafdebb674d622b4c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/62f696ad0d140e0e513e69eaafdebb674d622b4c", "type": "zip", "shasum": "", "reference": "62f696ad0d140e0e513e69eaafdebb674d622b4c"}, "time": "2020-06-15T13:10:07+00:00", "require": {"php": "^7.3"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "7579d5a1ba7f3ac11c80004d205877911315ae7a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/7579d5a1ba7f3ac11c80004d205877911315ae7a", "type": "zip", "shasum": "", "reference": "7579d5a1ba7f3ac11c80004d205877911315ae7a"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.0.0"}, "time": "2020-02-07T06:06:11+00:00", "funding": "__unset"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "9ee24c724374ba355385e98808da8b4343b28f67"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/9ee24c724374ba355385e98808da8b4343b28f67", "type": "zip", "shasum": "", "reference": "9ee24c724374ba355385e98808da8b4343b28f67"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/2.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:22:55+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "require": {"php": ">=7.1", "ext-pcntl": "*"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "suggest": "__unset"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "4a01883f660b10d4a19a14de5efd19b22eac2d93"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/4a01883f660b10d4a19a14de5efd19b22eac2d93", "type": "zip", "shasum": "", "reference": "4a01883f660b10d4a19a14de5efd19b22eac2d93"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/master"}, "time": "2018-01-27T06:52:17+00:00", "require": {"php": "^7.1", "ext-pcntl": "*"}, "require-dev": {"phpunit/phpunit": "^6.5"}, "funding": "__unset"}, {"description": "Utility class for invoking callables with a timeout.", "version": "1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "86074bf0fc2caf02ec8819a93f65a37cd0b44c8e"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/86074bf0fc2caf02ec8819a93f65a37cd0b44c8e", "type": "zip", "shasum": "", "reference": "86074bf0fc2caf02ec8819a93f65a37cd0b44c8e"}, "time": "2015-06-21T13:32:55+00:00", "require": {"php": ">=5.3.3", "phpunit/php-timer": ">=1.0.6", "ext-pcntl": "*"}, "require-dev": {"phpunit/phpunit": "~4"}, "extra": "__unset"}, {"version": "1.1.3", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "8696484458cb43eed025ab46260846de5b74655c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/8696484458cb43eed025ab46260846de5b74655c", "type": "zip", "shasum": "", "reference": "8696484458cb43eed025ab46260846de5b74655c"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/1.1"}, "time": "2013-07-16T05:20:21+00:00", "autoload": {"classmap": ["PHP/"]}, "include-path": [""], "require": {"php": ">=5.2.7", "phpunit/php-timer": ">=1.0.4,<1.1.0", "ext-pcntl": "*"}, "require-dev": "__unset"}, {"homepage": "http://www.phpunit.de/", "version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "6a96ba4a41a8ab1861d1f8567f1e4ea20be73333"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/6a96ba4a41a8ab1861d1f8567f1e4ea20be73333", "type": "zip", "shasum": "", "reference": "6a96ba4a41a8ab1861d1f8567f1e4ea20be73333"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/1.1.2"}, "time": "2012-10-05T16:20:45+00:00", "require": {"php": ">=5.2.7", "phpunit/php-timer": ">=1.0.1@stable", "ext-pcntl": "*"}}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "type": "git", "reference": "8563d324ee9539b02cc640300983cdaf2e2b6201"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/8563d324ee9539b02cc640300983cdaf2e2b6201", "type": "zip", "shasum": "", "reference": "8563d324ee9539b02cc640300983cdaf2e2b6201"}, "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/1.1.1"}, "time": "2012-09-23T00:00:00+00:00", "autoload": {"files": []}}]}, "security-advisories": [], "last-modified": "Fri, 07 Feb 2025 04:59:11 GMT"}