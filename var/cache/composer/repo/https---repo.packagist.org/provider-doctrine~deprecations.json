{"minified": "composer/2.0", "packages": {"doctrine/deprecations": [{"name": "doctrine/deprecations", "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "keywords": [], "homepage": "https://www.doctrine-project.org/", "version": "1.1.5", "version_normalized": "*******", "license": ["MIT"], "authors": [], "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "type": "zip", "shasum": "", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "type": "library", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "funding": [], "time": "2025-04-07T20:06:18+00:00", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}}, {"version": "1.1.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "31610dbb31faa98e6b5447b62340826f54fbc4e9"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/31610dbb31faa98e6b5447b62340826f54fbc4e9", "type": "zip", "shasum": "", "reference": "31610dbb31faa98e6b5447b62340826f54fbc4e9"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.4"}, "time": "2024-12-07T21:18:45+00:00", "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "1.4.10 || 2.0.3", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/log": "^1 || ^2 || ^3"}, "conflict": "__unset"}, {"version": "1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "type": "zip", "shasum": "", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.3"}, "time": "2024-01-30T19:34:25+00:00", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "4f2d4f2836e7ec4e7a8625e75c6aa916004db931"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/4f2d4f2836e7ec4e7a8625e75c6aa916004db931", "type": "zip", "shasum": "", "reference": "4f2d4f2836e7ec4e7a8625e75c6aa916004db931"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.2"}, "time": "2023-09-27T20:04:15+00:00"}, {"version": "v1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "612a3ee5ab0d5dd97b7cf3874a6efe24325efac3"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/612a3ee5ab0d5dd97b7cf3874a6efe24325efac3", "type": "zip", "shasum": "", "reference": "612a3ee5ab0d5dd97b7cf3874a6efe24325efac3"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v1.1.1"}, "time": "2023-06-03T09:27:29+00:00"}, {"version": "v1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "8cffffb2218e01f3b370bf763e00e81697725259"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/8cffffb2218e01f3b370bf763e00e81697725259", "type": "zip", "shasum": "", "reference": "8cffffb2218e01f3b370bf763e00e81697725259"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v1.1.0"}, "time": "2023-05-29T18:55:17+00:00", "require": {"php": "^7.1|^8.0"}, "require-dev": {"phpunit/phpunit": "^7.5|^8.5|^9.5", "psr/log": "^1|^2|^3", "doctrine/coding-standard": "^9"}}, {"version": "v1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de", "type": "zip", "shasum": "", "reference": "0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v1.0.0"}, "time": "2022-05-02T15:47:09+00:00"}, {"version": "v0.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "9504165960a1f83cc1480e2be1dd0a0478561314"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/9504165960a1f83cc1480e2be1dd0a0478561314", "type": "zip", "shasum": "", "reference": "9504165960a1f83cc1480e2be1dd0a0478561314"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v0.5.3"}, "time": "2021-03-21T12:59:47+00:00", "require-dev": {"phpunit/phpunit": "^7.0|^8.0|^9.0", "psr/log": "^1.0", "doctrine/coding-standard": "^6.0|^7.0|^8.0"}}, {"version": "v0.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "d7eade76c6bc9ba6a2a4ee9a445c5bed772634ef"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/d7eade76c6bc9ba6a2a4ee9a445c5bed772634ef", "type": "zip", "shasum": "", "reference": "d7eade76c6bc9ba6a2a4ee9a445c5bed772634ef"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v0.5.2"}, "time": "2021-03-13T20:55:25+00:00"}, {"version": "v0.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "98cd98dbcd7275e28c75a50ee9197665c81a0792"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/98cd98dbcd7275e28c75a50ee9197665c81a0792", "type": "zip", "shasum": "", "reference": "98cd98dbcd7275e28c75a50ee9197665c81a0792"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v0.5.1"}, "time": "2021-03-11T21:30:59+00:00"}, {"version": "v0.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "db319bc67adf1f394cd34a3dc65cad41541b998f"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/db319bc67adf1f394cd34a3dc65cad41541b998f", "type": "zip", "shasum": "", "reference": "db319bc67adf1f394cd34a3dc65cad41541b998f"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v0.5.0"}, "time": "2021-03-11T21:10:27+00:00"}, {"version": "v0.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "011e779a77b8ad11ceaf7ff6c083d324275d11eb"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/011e779a77b8ad11ceaf7ff6c083d324275d11eb", "type": "zip", "shasum": "", "reference": "011e779a77b8ad11ceaf7ff6c083d324275d11eb"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v0.4.0"}, "time": "2021-03-11T19:10:03+00:00"}, {"version": "v0.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "4cdeed49547103e0379443abfce9f14f0a48426b"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/4cdeed49547103e0379443abfce9f14f0a48426b", "type": "zip", "shasum": "", "reference": "4cdeed49547103e0379443abfce9f14f0a48426b"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v0.3.0"}, "time": "2021-03-11T13:07:48+00:00"}, {"version": "v0.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "c01bb1681fcfe9a655382cf3e2f772f59afec33e"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/c01bb1681fcfe9a655382cf3e2f772f59afec33e", "type": "zip", "shasum": "", "reference": "c01bb1681fcfe9a655382cf3e2f772f59afec33e"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v0.2.0"}, "time": "2021-02-07T19:28:17+00:00"}, {"version": "v0.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/deprecations.git", "type": "git", "reference": "9d2dfb1eedb9525ca28bb69620b0914af55a2048"}, "dist": {"url": "https://api.github.com/repos/doctrine/deprecations/zipball/9d2dfb1eedb9525ca28bb69620b0914af55a2048", "type": "zip", "shasum": "", "reference": "9d2dfb1eedb9525ca28bb69620b0914af55a2048"}, "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v0.1.0"}, "time": "2020-12-30T08:51:01+00:00"}]}, "security-advisories": [], "last-modified": "Mon, 07 Apr 2025 20:07:14 GMT"}