<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'WideImage\\Canvas' => $baseDir . '/libraries/WideImage/Canvas.php',
    'WideImage\\Coordinate' => $baseDir . '/libraries/WideImage/Coordinate.php',
    'WideImage\\Exception\\Exception' => $baseDir . '/libraries/WideImage/Exception/Exception.php',
    'WideImage\\Exception\\GDFunctionResultException' => $baseDir . '/libraries/WideImage/Exception/GDFunctionResultException.php',
    'WideImage\\Exception\\InvalidCanvasMethodException' => $baseDir . '/libraries/WideImage/Exception/InvalidCanvasMethodException.php',
    'WideImage\\Exception\\InvalidCoordinateException' => $baseDir . '/libraries/WideImage/Exception/InvalidCoordinateException.php',
    'WideImage\\Exception\\InvalidFontFileException' => $baseDir . '/libraries/WideImage/Exception/InvalidFontFileException.php',
    'WideImage\\Exception\\InvalidImageDimensionException' => $baseDir . '/libraries/WideImage/Exception/InvalidImageDimensionException.php',
    'WideImage\\Exception\\InvalidImageHandleException' => $baseDir . '/libraries/WideImage/Exception/InvalidImageHandleException.php',
    'WideImage\\Exception\\InvalidImageSourceException' => $baseDir . '/libraries/WideImage/Exception/InvalidImageSourceException.php',
    'WideImage\\Exception\\NoFontException' => $baseDir . '/libraries/WideImage/Exception/NoFontException.php',
    'WideImage\\Exception\\UnknownErrorWhileMappingException' => $baseDir . '/libraries/WideImage/Exception/UnknownErrorWhileMappingException.php',
    'WideImage\\Exception\\UnknownImageOperationException' => $baseDir . '/libraries/WideImage/Exception/UnknownImageOperationException.php',
    'WideImage\\Exception\\UnsupportedFormatException' => $baseDir . '/libraries/WideImage/Exception/UnsupportedFormatException.php',
    'WideImage\\Font\\GDF' => $baseDir . '/libraries/WideImage/Font/GDF.php',
    'WideImage\\Font\\PS' => $baseDir . '/libraries/WideImage/Font/PS.php',
    'WideImage\\Font\\TTF' => $baseDir . '/libraries/WideImage/Font/TTF.php',
    'WideImage\\Image' => $baseDir . '/libraries/WideImage/Image.php',
    'WideImage\\MapperFactory' => $baseDir . '/libraries/WideImage/MapperFactory.php',
    'WideImage\\Mapper\\BMP' => $baseDir . '/libraries/WideImage/Mapper/BMP.php',
    'WideImage\\Mapper\\GD' => $baseDir . '/libraries/WideImage/Mapper/GD.php',
    'WideImage\\Mapper\\GD2' => $baseDir . '/libraries/WideImage/Mapper/GD2.php',
    'WideImage\\Mapper\\GIF' => $baseDir . '/libraries/WideImage/Mapper/GIF.php',
    'WideImage\\Mapper\\JPEG' => $baseDir . '/libraries/WideImage/Mapper/JPEG.php',
    'WideImage\\Mapper\\PNG' => $baseDir . '/libraries/WideImage/Mapper/PNG.php',
    'WideImage\\Mapper\\TGA' => $baseDir . '/libraries/WideImage/Mapper/TGA.php',
    'WideImage\\Mapper\\WEBP' => $baseDir . '/libraries/WideImage/Mapper/WEBP.php',
    'WideImage\\OperationFactory' => $baseDir . '/libraries/WideImage/OperationFactory.php',
    'WideImage\\Operation\\AddNoise' => $baseDir . '/libraries/WideImage/Operation/AddNoise.php',
    'WideImage\\Operation\\ApplyConvolution' => $baseDir . '/libraries/WideImage/Operation/ApplyConvolution.php',
    'WideImage\\Operation\\ApplyFilter' => $baseDir . '/libraries/WideImage/Operation/ApplyFilter.php',
    'WideImage\\Operation\\ApplyMask' => $baseDir . '/libraries/WideImage/Operation/ApplyMask.php',
    'WideImage\\Operation\\AsGrayscale' => $baseDir . '/libraries/WideImage/Operation/AsGrayscale.php',
    'WideImage\\Operation\\AsNegative' => $baseDir . '/libraries/WideImage/Operation/AsNegative.php',
    'WideImage\\Operation\\AutoCrop' => $baseDir . '/libraries/WideImage/Operation/AutoCrop.php',
    'WideImage\\Operation\\CopyChannelsPalette' => $baseDir . '/libraries/WideImage/Operation/CopyChannelsPalette.php',
    'WideImage\\Operation\\CopyChannelsTrueColor' => $baseDir . '/libraries/WideImage/Operation/CopyChannelsTrueColor.php',
    'WideImage\\Operation\\CorrectGamma' => $baseDir . '/libraries/WideImage/Operation/CorrectGamma.php',
    'WideImage\\Operation\\Crop' => $baseDir . '/libraries/WideImage/Operation/Crop.php',
    'WideImage\\Operation\\Exception\\InvalidFitMethodException' => $baseDir . '/libraries/WideImage/Operation/Exception/InvalidFitMethodException.php',
    'WideImage\\Operation\\Exception\\InvalidResizeDimensionException' => $baseDir . '/libraries/WideImage/Operation/Exception/InvalidResizeDimensionException.php',
    'WideImage\\Operation\\Flip' => $baseDir . '/libraries/WideImage/Operation/Flip.php',
    'WideImage\\Operation\\GetMask' => $baseDir . '/libraries/WideImage/Operation/GetMask.php',
    'WideImage\\Operation\\Merge' => $baseDir . '/libraries/WideImage/Operation/Merge.php',
    'WideImage\\Operation\\Mirror' => $baseDir . '/libraries/WideImage/Operation/Mirror.php',
    'WideImage\\Operation\\Resize' => $baseDir . '/libraries/WideImage/Operation/Resize.php',
    'WideImage\\Operation\\ResizeCanvas' => $baseDir . '/libraries/WideImage/Operation/ResizeCanvas.php',
    'WideImage\\Operation\\Rotate' => $baseDir . '/libraries/WideImage/Operation/Rotate.php',
    'WideImage\\Operation\\RoundCorners' => $baseDir . '/libraries/WideImage/Operation/RoundCorners.php',
    'WideImage\\Operation\\Unsharp' => $baseDir . '/libraries/WideImage/Operation/Unsharp.php',
    'WideImage\\PaletteImage' => $baseDir . '/libraries/WideImage/PaletteImage.php',
    'WideImage\\TrueColorImage' => $baseDir . '/libraries/WideImage/TrueColorImage.php',
    'WideImage\\WideImage' => $baseDir . '/libraries/WideImage/WideImage.php',
    'WideImage\\vendor\\de77\\BMP' => $baseDir . '/libraries/WideImage/vendor/de77/BMP.php',
    'WideImage\\vendor\\de77\\TGA' => $baseDir . '/libraries/WideImage/vendor/de77/TGA.php',
    'icms' => $baseDir . '/libraries/icms.php',
    'icms_AutoloadPerformanceMonitor' => $baseDir . '/libraries/icms/AutoloadPerformanceMonitor.php',
    'icms_Autoloader' => $baseDir . '/libraries/icms/Autoloader.php',
    'icms_ComposerAutoloadBridge' => $baseDir . '/libraries/icms/ComposerAutoloadBridge.php',
    'icms_Event' => $baseDir . '/libraries/icms/Event.php',
    'icms_Utils' => $baseDir . '/libraries/icms/Utils.php',
    'icms_auth_Ads' => $baseDir . '/libraries/icms/auth/Ads.php',
    'icms_auth_Factory' => $baseDir . '/libraries/icms/auth/Factory.php',
    'icms_auth_Ldap' => $baseDir . '/libraries/icms/auth/Ldap.php',
    'icms_auth_Object' => $baseDir . '/libraries/icms/auth/Object.php',
    'icms_auth_Provisionning' => $baseDir . '/libraries/icms/auth/Provisionning.php',
    'icms_auth_Xoops' => $baseDir . '/libraries/icms/auth/Xoops.php',
    'icms_config_Handler' => $baseDir . '/libraries/icms/config/Handler.php',
    'icms_config_category_Handler' => $baseDir . '/libraries/icms/config/category/Handler.php',
    'icms_config_category_Object' => $baseDir . '/libraries/icms/config/category/Object.php',
    'icms_config_option_Handler' => $baseDir . '/libraries/icms/config/option/Handler.php',
    'icms_config_option_Object' => $baseDir . '/libraries/icms/config/option/Object.php',
    'icms_core_DataFilter' => $baseDir . '/libraries/icms/core/DataFilter.php',
    'icms_core_Debug' => $baseDir . '/libraries/icms/core/Debug.php',
    'icms_core_Filesystem' => $baseDir . '/libraries/icms/core/Filesystem.php',
    'icms_core_HTMLFilter' => $baseDir . '/libraries/icms/core/HTMLFilter.php',
    'icms_core_Logger' => $baseDir . '/libraries/icms/core/Logger.php',
    'icms_core_Message' => $baseDir . '/libraries/icms/core/Message.php',
    'icms_core_Object' => $baseDir . '/libraries/icms/core/Object.php',
    'icms_core_ObjectHandler' => $baseDir . '/libraries/icms/core/ObjectHandler.php',
    'icms_core_OnlineHandler' => $baseDir . '/libraries/icms/core/OnlineHandler.php',
    'icms_core_Password' => $baseDir . '/libraries/icms/core/Password.php',
    'icms_core_Security' => $baseDir . '/libraries/icms/core/Security.php',
    'icms_core_Session' => $baseDir . '/libraries/icms/core/Session.php',
    'icms_core_StopSpammer' => $baseDir . '/libraries/icms/core/StopSpammer.php',
    'icms_core_Textsanitizer' => $baseDir . '/libraries/icms/core/Textsanitizer.php',
    'icms_core_Versionchecker' => $baseDir . '/libraries/icms/core/Versionchecker.php',
    'icms_core_VersioncheckerInterface' => $baseDir . '/libraries/icms/core/VersioncheckerInterface.php',
    'icms_core_Versioncheckergithub' => $baseDir . '/libraries/icms/core/Versioncheckergithub.php',
    'icms_data_avatar_Handler' => $baseDir . '/libraries/icms/data/avatar/Handler.php',
    'icms_data_avatar_Object' => $baseDir . '/libraries/icms/data/avatar/Object.php',
    'icms_data_comment_Handler' => $baseDir . '/libraries/icms/data/comment/Handler.php',
    'icms_data_comment_Object' => $baseDir . '/libraries/icms/data/comment/Object.php',
    'icms_data_comment_Renderer' => $baseDir . '/libraries/icms/data/comment/Renderer.php',
    'icms_data_file_Handler' => $baseDir . '/libraries/icms/data/file/Handler.php',
    'icms_data_file_Object' => $baseDir . '/libraries/icms/data/file/Object.php',
    'icms_data_notification_Handler' => $baseDir . '/libraries/icms/data/notification/Handler.php',
    'icms_data_notification_Object' => $baseDir . '/libraries/icms/data/notification/Object.php',
    'icms_data_page_Handler' => $baseDir . '/libraries/icms/data/page/Handler.php',
    'icms_data_page_Object' => $baseDir . '/libraries/icms/data/page/Object.php',
    'icms_data_privmessage_Handler' => $baseDir . '/libraries/icms/data/privmessage/Handler.php',
    'icms_data_privmessage_Object' => $baseDir . '/libraries/icms/data/privmessage/Object.php',
    'icms_data_urllink_Handler' => $baseDir . '/libraries/icms/data/urllink/Handler.php',
    'icms_data_urllink_Object' => $baseDir . '/libraries/icms/data/urllink/Object.php',
    'icms_db_Connection' => $baseDir . '/libraries/icms/db/Connection.php',
    'icms_db_Factory' => $baseDir . '/libraries/icms/db/Factory.php',
    'icms_db_IConnection' => $baseDir . '/libraries/icms/db/IConnection.php',
    'icms_db_IUtility' => $baseDir . '/libraries/icms/db/IUtility.php',
    'icms_db_criteria_Compo' => $baseDir . '/libraries/icms/db/criteria/Compo.php',
    'icms_db_criteria_Element' => $baseDir . '/libraries/icms/db/criteria/Element.php',
    'icms_db_criteria_Item' => $baseDir . '/libraries/icms/db/criteria/Item.php',
    'icms_db_legacy_Database' => $baseDir . '/libraries/icms/db/legacy/Database.php',
    'icms_db_legacy_Factory' => $baseDir . '/libraries/icms/db/legacy/Factory.php',
    'icms_db_legacy_IDatabase' => $baseDir . '/libraries/icms/db/legacy/IDatabase.php',
    'icms_db_legacy_PdoDatabase' => $baseDir . '/libraries/icms/db/legacy/PdoDatabase.php',
    'icms_db_legacy_mysql_Database' => $baseDir . '/libraries/icms/db/legacy/mysql/Database.php',
    'icms_db_legacy_mysql_Proxy' => $baseDir . '/libraries/icms/db/legacy/mysql/Proxy.php',
    'icms_db_legacy_mysql_Safe' => $baseDir . '/libraries/icms/db/legacy/mysql/Safe.php',
    'icms_db_legacy_mysql_Utility' => $baseDir . '/libraries/icms/db/legacy/mysql/Utility.php',
    'icms_db_legacy_updater_Handler' => $baseDir . '/libraries/icms/db/legacy/updater/Handler.php',
    'icms_db_legacy_updater_Table' => $baseDir . '/libraries/icms/db/legacy/updater/Table.php',
    'icms_db_mysql_Connection' => $baseDir . '/libraries/icms/db/mysql/Connection.php',
    'icms_db_mysql_Utility' => $baseDir . '/libraries/icms/db/mysql/Utility.php',
    'icms_feeds_Rss' => $baseDir . '/libraries/icms/feeds/Rss.php',
    'icms_feeds_Simplerss' => $baseDir . '/libraries/icms/feeds/Simplerss.php',
    'icms_file_DownloadHandler' => $baseDir . '/libraries/icms/file/DownloadHandler.php',
    'icms_file_MediaUploadHandler' => $baseDir . '/libraries/icms/file/MediaUploadHandler.php',
    'icms_file_TarDownloader' => $baseDir . '/libraries/icms/file/TarDownloader.php',
    'icms_file_TarFileHandler' => $baseDir . '/libraries/icms/file/TarFileHandler.php',
    'icms_file_ZipDownloader' => $baseDir . '/libraries/icms/file/ZipDownloader.php',
    'icms_file_ZipFileHandler' => $baseDir . '/libraries/icms/file/ZipFileHandler.php',
    'icms_form_Base' => $baseDir . '/libraries/icms/form/Base.php',
    'icms_form_Element' => $baseDir . '/libraries/icms/form/Element.php',
    'icms_form_Groupperm' => $baseDir . '/libraries/icms/form/Groupperm.php',
    'icms_form_Simple' => $baseDir . '/libraries/icms/form/Simple.php',
    'icms_form_Table' => $baseDir . '/libraries/icms/form/Table.php',
    'icms_form_Theme' => $baseDir . '/libraries/icms/form/Theme.php',
    'icms_form_elements_Button' => $baseDir . '/libraries/icms/form/elements/Button.php',
    'icms_form_elements_Captcha' => $baseDir . '/libraries/icms/form/elements/Captcha.php',
    'icms_form_elements_Checkbox' => $baseDir . '/libraries/icms/form/elements/Checkbox.php',
    'icms_form_elements_Colorpicker' => $baseDir . '/libraries/icms/form/elements/Colorpicker.php',
    'icms_form_elements_Date' => $baseDir . '/libraries/icms/form/elements/Date.php',
    'icms_form_elements_Datetime' => $baseDir . '/libraries/icms/form/elements/Datetime.php',
    'icms_form_elements_Dhtmltextarea' => $baseDir . '/libraries/icms/form/elements/Dhtmltextarea.php',
    'icms_form_elements_Editor' => $baseDir . '/libraries/icms/form/elements/Editor.php',
    'icms_form_elements_File' => $baseDir . '/libraries/icms/form/elements/File.php',
    'icms_form_elements_Groupperm' => $baseDir . '/libraries/icms/form/elements/Groupperm.php',
    'icms_form_elements_Hidden' => $baseDir . '/libraries/icms/form/elements/Hidden.php',
    'icms_form_elements_Hiddentoken' => $baseDir . '/libraries/icms/form/elements/Hiddentoken.php',
    'icms_form_elements_Label' => $baseDir . '/libraries/icms/form/elements/Label.php',
    'icms_form_elements_Password' => $baseDir . '/libraries/icms/form/elements/Password.php',
    'icms_form_elements_Radio' => $baseDir . '/libraries/icms/form/elements/Radio.php',
    'icms_form_elements_Radioyn' => $baseDir . '/libraries/icms/form/elements/Radioyn.php',
    'icms_form_elements_Select' => $baseDir . '/libraries/icms/form/elements/Select.php',
    'icms_form_elements_Text' => $baseDir . '/libraries/icms/form/elements/Text.php',
    'icms_form_elements_Textarea' => $baseDir . '/libraries/icms/form/elements/Textarea.php',
    'icms_form_elements_Tray' => $baseDir . '/libraries/icms/form/elements/Tray.php',
    'icms_form_elements_captcha_Image' => $baseDir . '/libraries/icms/form/elements/captcha/Image.php',
    'icms_form_elements_captcha_ImageHandler' => $baseDir . '/libraries/icms/form/elements/captcha/ImageHandler.php',
    'icms_form_elements_captcha_Object' => $baseDir . '/libraries/icms/form/elements/captcha/Object.php',
    'icms_form_elements_captcha_Text' => $baseDir . '/libraries/icms/form/elements/captcha/Text.php',
    'icms_form_elements_select_Country' => $baseDir . '/libraries/icms/form/elements/select/Country.php',
    'icms_form_elements_select_Editor' => $baseDir . '/libraries/icms/form/elements/select/Editor.php',
    'icms_form_elements_select_Group' => $baseDir . '/libraries/icms/form/elements/select/Group.php',
    'icms_form_elements_select_Image' => $baseDir . '/libraries/icms/form/elements/select/Image.php',
    'icms_form_elements_select_Lang' => $baseDir . '/libraries/icms/form/elements/select/Lang.php',
    'icms_form_elements_select_Matchoption' => $baseDir . '/libraries/icms/form/elements/select/Matchoption.php',
    'icms_form_elements_select_Theme' => $baseDir . '/libraries/icms/form/elements/select/Theme.php',
    'icms_form_elements_select_Timezone' => $baseDir . '/libraries/icms/form/elements/select/Timezone.php',
    'icms_form_elements_select_User' => $baseDir . '/libraries/icms/form/elements/select/User.php',
    'icms_image_Handler' => $baseDir . '/libraries/icms/image/Handler.php',
    'icms_image_Object' => $baseDir . '/libraries/icms/image/Object.php',
    'icms_image_category_Handler' => $baseDir . '/libraries/icms/image/category/Handler.php',
    'icms_image_category_Object' => $baseDir . '/libraries/icms/image/category/Object.php',
    'icms_image_set_Handler' => $baseDir . '/libraries/icms/image/set/Handler.php',
    'icms_image_set_Object' => $baseDir . '/libraries/icms/image/set/Object.php',
    'icms_ipf_About' => $baseDir . '/libraries/icms/ipf/About.php',
    'icms_ipf_Controller' => $baseDir . '/libraries/icms/ipf/Controller.php',
    'icms_ipf_Handler' => $baseDir . '/libraries/icms/ipf/Handler.php',
    'icms_ipf_Highlighter' => $baseDir . '/libraries/icms/ipf/Highlighter.php',
    'icms_ipf_Metagen' => $baseDir . '/libraries/icms/ipf/Metagen.php',
    'icms_ipf_Object' => $baseDir . '/libraries/icms/ipf/Object.php',
    'icms_ipf_Tree' => $baseDir . '/libraries/icms/ipf/Tree.php',
    'icms_ipf_category_Handler' => $baseDir . '/libraries/icms/ipf/category/Handler.php',
    'icms_ipf_category_Object' => $baseDir . '/libraries/icms/ipf/category/Object.php',
    'icms_ipf_export_Handler' => $baseDir . '/libraries/icms/ipf/export/Handler.php',
    'icms_ipf_export_Renderer' => $baseDir . '/libraries/icms/ipf/export/Renderer.php',
    'icms_ipf_form_Base' => $baseDir . '/libraries/icms/ipf/form/Base.php',
    'icms_ipf_form_Secure' => $baseDir . '/libraries/icms/ipf/form/Secure.php',
    'icms_ipf_form_elements_Autocomplete' => $baseDir . '/libraries/icms/ipf/form/elements/Autocomplete.php',
    'icms_ipf_form_elements_Blockoptions' => $baseDir . '/libraries/icms/ipf/form/elements/Blockoptions.php',
    'icms_ipf_form_elements_Checkbox' => $baseDir . '/libraries/icms/ipf/form/elements/Checkbox.php',
    'icms_ipf_form_elements_Date' => $baseDir . '/libraries/icms/ipf/form/elements/Date.php',
    'icms_ipf_form_elements_Datetime' => $baseDir . '/libraries/icms/ipf/form/elements/Datetime.php',
    'icms_ipf_form_elements_File' => $baseDir . '/libraries/icms/ipf/form/elements/File.php',
    'icms_ipf_form_elements_Fileupload' => $baseDir . '/libraries/icms/ipf/form/elements/Fileupload.php',
    'icms_ipf_form_elements_Image' => $baseDir . '/libraries/icms/ipf/form/elements/Image.php',
    'icms_ipf_form_elements_Imageupload' => $baseDir . '/libraries/icms/ipf/form/elements/Imageupload.php',
    'icms_ipf_form_elements_Language' => $baseDir . '/libraries/icms/ipf/form/elements/Language.php',
    'icms_ipf_form_elements_Page' => $baseDir . '/libraries/icms/ipf/form/elements/Page.php',
    'icms_ipf_form_elements_Parentcategory' => $baseDir . '/libraries/icms/ipf/form/elements/Parentcategory.php',
    'icms_ipf_form_elements_Passwordtray' => $baseDir . '/libraries/icms/ipf/form/elements/Passwordtray.php',
    'icms_ipf_form_elements_Radio' => $baseDir . '/libraries/icms/ipf/form/elements/Radio.php',
    'icms_ipf_form_elements_Richfile' => $baseDir . '/libraries/icms/ipf/form/elements/Richfile.php',
    'icms_ipf_form_elements_Section' => $baseDir . '/libraries/icms/ipf/form/elements/Section.php',
    'icms_ipf_form_elements_Select' => $baseDir . '/libraries/icms/ipf/form/elements/Select.php',
    'icms_ipf_form_elements_Selectmulti' => $baseDir . '/libraries/icms/ipf/form/elements/Selectmulti.php',
    'icms_ipf_form_elements_Signature' => $baseDir . '/libraries/icms/ipf/form/elements/Signature.php',
    'icms_ipf_form_elements_Source' => $baseDir . '/libraries/icms/ipf/form/elements/Source.php',
    'icms_ipf_form_elements_Text' => $baseDir . '/libraries/icms/ipf/form/elements/Text.php',
    'icms_ipf_form_elements_Time' => $baseDir . '/libraries/icms/ipf/form/elements/Time.php',
    'icms_ipf_form_elements_Upload' => $baseDir . '/libraries/icms/ipf/form/elements/Upload.php',
    'icms_ipf_form_elements_Urllink' => $baseDir . '/libraries/icms/ipf/form/elements/Urllink.php',
    'icms_ipf_form_elements_User' => $baseDir . '/libraries/icms/ipf/form/elements/User.php',
    'icms_ipf_form_elements_Yesno' => $baseDir . '/libraries/icms/ipf/form/elements/Yesno.php',
    'icms_ipf_member_Handler' => $baseDir . '/libraries/icms/ipf/member/Handler.php',
    'icms_ipf_permission_Handler' => $baseDir . '/libraries/icms/ipf/permission/Handler.php',
    'icms_ipf_registry_Handler' => $baseDir . '/libraries/icms/ipf/registry/Handler.php',
    'icms_ipf_seo_Object' => $baseDir . '/libraries/icms/ipf/seo/Object.php',
    'icms_ipf_view_Column' => $baseDir . '/libraries/icms/ipf/view/Column.php',
    'icms_ipf_view_Row' => $baseDir . '/libraries/icms/ipf/view/Row.php',
    'icms_ipf_view_Single' => $baseDir . '/libraries/icms/ipf/view/Single.php',
    'icms_ipf_view_Table' => $baseDir . '/libraries/icms/ipf/view/Table.php',
    'icms_ipf_view_Tree' => $baseDir . '/libraries/icms/ipf/view/Tree.php',
    'icms_member_Handler' => $baseDir . '/libraries/icms/member/Handler.php',
    'icms_member_group_Handler' => $baseDir . '/libraries/icms/member/group/Handler.php',
    'icms_member_group_Object' => $baseDir . '/libraries/icms/member/group/Object.php',
    'icms_member_group_membership_Handler' => $baseDir . '/libraries/icms/member/group/membership/Handler.php',
    'icms_member_group_membership_Object' => $baseDir . '/libraries/icms/member/group/membership/Object.php',
    'icms_member_groupperm_Handler' => $baseDir . '/libraries/icms/member/groupperm/Handler.php',
    'icms_member_groupperm_Object' => $baseDir . '/libraries/icms/member/groupperm/Object.php',
    'icms_member_user_Handler' => $baseDir . '/libraries/icms/member/user/Handler.php',
    'icms_member_user_Object' => $baseDir . '/libraries/icms/member/user/Object.php',
    'icms_messaging_EmailHandler' => $baseDir . '/libraries/icms/messaging/EmailHandler.php',
    'icms_messaging_Handler' => $baseDir . '/libraries/icms/messaging/Handler.php',
    'icms_module_Handler' => $baseDir . '/libraries/icms/module/Handler.php',
    'icms_module_Object' => $baseDir . '/libraries/icms/module/Object.php',
    'icms_plugins_EditorHandler' => $baseDir . '/libraries/icms/plugins/EditorHandler.php',
    'icms_plugins_Handler' => $baseDir . '/libraries/icms/plugins/Handler.php',
    'icms_plugins_Object' => $baseDir . '/libraries/icms/plugins/Object.php',
    'icms_preload_Handler' => $baseDir . '/libraries/icms/preload/Handler.php',
    'icms_preload_Item' => $baseDir . '/libraries/icms/preload/Item.php',
    'icms_preload_LibrariesHandler' => $baseDir . '/libraries/icms/preload/LibrariesHandler.php',
    'icms_sys_autotasks_ISystem' => $baseDir . '/libraries/icms/sys/autotasks/ISystem.php',
    'icms_sys_autotasks_System' => $baseDir . '/libraries/icms/sys/autotasks/System.php',
    'icms_view_Breadcrumb' => $baseDir . '/libraries/icms/view/Breadcrumb.php',
    'icms_view_PageBuilder' => $baseDir . '/libraries/icms/view/PageBuilder.php',
    'icms_view_PageNav' => $baseDir . '/libraries/icms/view/PageNav.php',
    'icms_view_Printerfriendly' => $baseDir . '/libraries/icms/view/Printerfriendly.php',
    'icms_view_Tpl' => $baseDir . '/libraries/icms/view/Tpl.php',
    'icms_view_Tree' => $baseDir . '/libraries/icms/view/Tree.php',
    'icms_view_block_Handler' => $baseDir . '/libraries/icms/view/block/Handler.php',
    'icms_view_block_Object' => $baseDir . '/libraries/icms/view/block/Object.php',
    'icms_view_block_position_Handler' => $baseDir . '/libraries/icms/view/block/position/Handler.php',
    'icms_view_block_position_Object' => $baseDir . '/libraries/icms/view/block/position/Object.php',
    'icms_view_template_file_Handler' => $baseDir . '/libraries/icms/view/template/file/Handler.php',
    'icms_view_template_file_Object' => $baseDir . '/libraries/icms/view/template/file/Object.php',
    'icms_view_template_set_Handler' => $baseDir . '/libraries/icms/view/template/set/Handler.php',
    'icms_view_template_set_Object' => $baseDir . '/libraries/icms/view/template/set/Object.php',
    'icms_view_theme_Factory' => $baseDir . '/libraries/icms/view/theme/Factory.php',
    'icms_view_theme_Object' => $baseDir . '/libraries/icms/view/theme/Object.php',
);
