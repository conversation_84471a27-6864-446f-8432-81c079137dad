<?php
/**
 * Test script to verify installer can load autoloader correctly
 */

echo "Testing Installer Autoloading Integration\n";
echo "========================================\n\n";

// Test 1: Check file paths
echo "1. Checking file paths...\n";
$requiredFiles = [
    '../vendor/autoload.php',
    '../libraries/icms/ComposerAutoloadBridge.php',
    '../libraries/icms/Autoloader.php',
    '../include/version.php',
    '../include/functions.php',
    './class/IcmsInstallWizard.php'
];

$allFilesPresent = true;
foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "   ✓ {$file} found\n";
    } else {
        echo "   ✗ {$file} missing\n";
        $allFilesPresent = false;
    }
}

if (!$allFilesPresent) {
    echo "\nSome required files are missing. Installation cannot proceed.\n";
    exit(1);
}

// Test 2: Load Composer autoloader
echo "\n2. Loading Composer autoloader...\n";
if (file_exists('../vendor/autoload.php')) {
    require_once '../vendor/autoload.php';
    echo "   ✓ Composer autoloader loaded successfully\n";
} else {
    echo "   ✗ Composer autoloader not found\n";
    exit(1);
}

// Test 3: Load compatibility bridge
echo "\n3. Loading compatibility bridge...\n";
try {
    require_once '../libraries/icms/ComposerAutoloadBridge.php';
    echo "   ✓ Compatibility bridge loaded successfully\n";
} catch (Exception $e) {
    echo "   ✗ Failed to load compatibility bridge: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 4: Load legacy autoloader
echo "\n4. Loading legacy autoloader...\n";
try {
    require_once '../libraries/icms/Autoloader.php';
    icms_Autoloader::setup();
    echo "   ✓ Legacy autoloader loaded successfully\n";
} catch (Exception $e) {
    echo "   ✗ Failed to load legacy autoloader: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 5: Initialize bridge
echo "\n5. Initializing bridge...\n";
try {
    icms_ComposerAutoloadBridge::initialize();
    echo "   ✓ Bridge initialized successfully\n";
} catch (Exception $e) {
    echo "   ✗ Failed to initialize bridge: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 6: Test class loading
echo "\n6. Testing class loading...\n";
$testClasses = [
    'icms_core_Security',
    'icms_core_Logger',
    'icms_auth_Factory'
];

$loadedClasses = 0;
foreach ($testClasses as $className) {
    if (class_exists($className, true)) {
        echo "   ✓ {$className} loaded successfully\n";
        $loadedClasses++;
    } else {
        echo "   ✗ Failed to load {$className}\n";
    }
}

echo "\n   Summary: {$loadedClasses}/" . count($testClasses) . " classes loaded successfully\n";

// Test 7: Load installer-specific files
echo "\n7. Loading installer-specific files...\n";
try {
    include_once '../include/version.php';
    echo "   ✓ version.php loaded successfully\n";
} catch (Exception $e) {
    echo "   ✗ Failed to load version.php: " . $e->getMessage() . "\n";
}

try {
    include_once '../include/functions.php';
    echo "   ✓ functions.php loaded successfully\n";
} catch (Exception $e) {
    echo "   ✗ Failed to load functions.php: " . $e->getMessage() . "\n";
}

try {
    require_once 'include/functions.php';
    echo "   ✓ installer functions.php loaded successfully\n";
} catch (Exception $e) {
    echo "   ✗ Failed to load installer functions.php: " . $e->getMessage() . "\n";
}

try {
    include_once './class/IcmsInstallWizard.php';
    echo "   ✓ IcmsInstallWizard.php loaded successfully\n";
} catch (Exception $e) {
    echo "   ✗ Failed to load IcmsInstallWizard.php: " . $e->getMessage() . "\n";
}

echo "\n========================================\n";
echo "Installer autoloading test completed!\n";
echo "The installer should now work correctly with the new autoloading system.\n";
