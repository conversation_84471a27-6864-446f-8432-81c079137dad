	Usage of GNU FreeFont

Language scripts and faces
==========================

There are three faces (serif, sans-serif, and monospace), and four styles
(regular, bold, cursive/italic, and bold cursive/italic) for each face.
There is one font file per face/style combination: 12 files in total.

The letters for various languages, as well as specialized symbols, exist
among the various font files, but they are not uniformly populated.
All the fonts have complete support for Latin, Cyrillic, and Greek, as
well as most of the extensions for those scripts.

At this time, serif regular has by far the largest number of letters, and
supports the largest number of writing scripts.  However there are writing
scripts supported by the sans-serif but not by serif.

For an overview of which scripts and sets of symbols are supported by
which face, see the FreeFont 'coverage' web page.

Font features
=============

FreeFont has numerous font "features" that perform alterations to the basic
letters of the font, replacing them with other letters, or positioning them
with respect to other letters.

Many features are activated automatically, but in some environments, they
present some user control.  This documents those features with user control.

Language-specific features
==========================

Some OpenType font features are activated only when the text is specified to
be of a certain language. 

This is done in HTML by enclosing the text with a tag whose 'lang' attribute
is set to the appropriate ISO 632.2 language code.  In a word processor, 
any block of text can be given a language setting.

Latin
-----
Catalan	ligature improving l·l
Dutch	ligatures for ij, IJ
Sami	localized form for letter Eng
Turkish	overrides ligatures fi ffi of Latin

Cyrillic
--------
Ukrainian	ligature for double i-diaresis
Serbian/Macedonian	localized letters be, and more in italic
Bulgarian	style set for modern glyphs

Hebrew
------
Yiddish	raised vowels under yo

Devanagari
----------
Sanskrit	much larger set of ligatures

Hindi, Marathi	better spacing of Western punctuation marks

Indic languages
---------------

The 'danda' character is encoded in Unicode only in the Devanagari range.
When writing in scripts of other Indic languages, this same character is to
be used.  But the shapes and line thicknesses of glyphs vary slightly from
one script to another, so the same glyph for 'danda' may not fit all scripts.

By specifying the language of the text, an appropriate glyph for 'danda'
will be obtained.

Style sets
==========

These replacements are activated by specifying a "Style Set".
These features are accessible only from typesetting software.

Cyrillic	Bulgarian modern (ss01)

Devanagari	Bombay (ss02), Calcutta (ss03), Nepali (ss04)

Discretionary features
======================
These features are accessible only from typesetting software.
Typically the user must specifically request them.

Unless otherwise noted, these are available only in FreeSerif.

Ligatures and substitutions
---------------------------

Arabic, Armenian, Hebrew, German, Dutch

Small captials
--------------

A limited set of specially drawn small capital letters in Latin.

Superscript and subscript
-------------------------

Transform a limited set of characters--mostly Latin letters and numerals--
to versions well-sized and positioned as superscript or subscript.

Numeral styles
--------------

The default numerals of FreeSerif are mono-spaced and of even height.
It also features proportionally-spaced numerals, and "old-style" numerals--
those which vary in height and sometimes go beneath the baseline.
These can be had at discretion.

Diagonal fractions
------------------

A limited set of diagonal fraction substitutions are available at discretion.
The set is more than what is encoded in Unicode.
They work with the ASCII slash or the mathematical slash U+2215.
The transform a sequence "number-slash-number" to a diagonal form.

Zero
----

A slashed form of the numeral zero is available at discretion.
Available in all faces.

Alternative characters
======================

FreeSerif has some listings of alternatives for specific characters.
Again this is use primarily in specialized typesetting software.

Greek, Latin

Use in LaTeX
============
It is possible to use Unicode fonts in recent LaTeX implementations, but in
	LuaTeX http://www.luatex.org/ and
	XeTeX http://tug.org/xetex/
it is particularly easy to use Unicode text, and to enable font features.
Recent versions of these systems use the 'fontspec' package to choose fonts
and features.

A very simple document might contain the lines
---------------------------------------------------------------------------
\documentclass{ltxdockit}
\usepackage{fontspec}
\usepackage{xunicode}
\setmainfont[]{FreeSerif}
\begin{document}
{\fontspec[Script=Default,Fractions={On}]{FreeSerif}
1/7 3/10 7/10}

x\raisebox{-0.5ex}{{\scriptsize ai}}
x{\fontspec[Script=Default,VerticalPosition={Inferior}]{FreeSerif}
abcdefghijklmnopqrstuvwxyz+−(0123456789)} \\
x\raisebox{0.85ex}{{\scriptsize ai}}
x{\fontspec[Script=Default,VerticalPosition={Superior}]{FreeSerif}
abcdefghijklmnopqrstuvwxyz+−(0123456789)}

{\fontspec[Script=Latin]{FreeSerif}
\textsc{Small Caps} }

{ Bсички хора се раждат свободни и равни по достойнство и права.
\fontspec[Script=Cyrillic,Language=Bulgarian,Variant={1}]{FreeSerif} \selectfont
Bсички хора се раждат свободни и равни по достойнство и права.  }

\end{document}
---------------------------------------------------------------------------
Here are some 'fontspec' setting-value pairs meaningful for FreeFont.

Numbers: Lining OldStyle Proportional SlashedZero
Fractions: On
VerticalPosition: Superior Inferior
Ligatures: Common Historical
Letters: UppercaseSmallCaps
Variant: 1 (etc. -- must be in {} picks style set.)
---------------------------------------------------------------------------


$Id: usage.txt,v 1.10 2011-07-16 08:38:06 Stevan_White Exp $
