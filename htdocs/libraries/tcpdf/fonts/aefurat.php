<?php
// TCPDF FONT FILE DESCRIPTION
$type='TrueTypeUnicode';
$name='Furat';
$up=-136;
$ut=64;
$dw=367;
$diff='';
$originalsize=171060;
$enc='';
$file='aefurat.z';
$ctg='aefurat.ctg.z';
$desc=array('Flags'=>32,'FontBBox'=>'[-272 -501 1319 1114]','ItalicAngle'=>0,'Ascent'=>1162,'Descent'=>-508,'Leading'=>200,'CapHeight'=>662,'XHeight'=>450,'StemV'=>39,'StemH'=>17,'AvgWidth'=>401,'MaxWidth'=>1332,'MissingWidth'=>367);
$cbbox=array(33=>array(130,-9,237,676),34=>array(77,431,331,676),35=>array(6,0,496,662),36=>array(44,-87,457,727),37=>array(61,-13,772,676),38=>array(42,-13,750,676),39=>array(48,431,133,676),40=>array(48,-177,304,676),41=>array(29,-177,286,676),42=>array(69,266,432,676),43=>array(30,0,534,506),44=>array(56,-141,194,102),45=>array(39,194,286,257),46=>array(70,-11,181,100),47=>array(-9,-14,287,676),48=>array(24,-14,476,676),49=>array(111,0,394,676),50=>array(30,0,474,676),51=>array(43,-14,432,676),52=>array(12,0,472,676),53=>array(32,-14,438,688),54=>array(34,-14,468,684),55=>array(20,-8,449,662),56=>array(56,-14,446,676),57=>array(30,-22,459,676),58=>array(81,-11,192,459),59=>array(80,-141,219,459),60=>array(28,-10,536,516),61=>array(30,120,534,386),62=>array(28,-10,536,516),63=>array(68,-8,414,676),64=>array(116,-14,809,676),65=>array(14,0,706,674),66=>array(17,0,593,662),67=>array(28,-14,633,676),68=>array(16,0,686,662),69=>array(12,0,597,662),70=>array(12,0,546,662),71=>array(32,-14,709,676),72=>array(19,0,702,662),73=>array(18,0,314,662),74=>array(10,-14,370,662),75=>array(34,0,723,662),76=>array(12,0,598,662),77=>array(12,0,863,662),78=>array(12,-11,707,662),79=>array(34,-14,688,676),80=>array(16,0,542,662),81=>array(34,-178,701,676),82=>array(17,0,659,662),83=>array(42,-14,491,676),84=>array(17,0,593,662),85=>array(14,-14,706,662),86=>array(16,-11,697,662),87=>array(6,-11,932,662),88=>array(10,0,704,662),89=>array(22,0,703,662),90=>array(9,0,597,662),91=>array(88,-156,299,662),92=>array(-9,-14,287,676),93=>array(34,-156,246,662),94=>array(24,297,446,662),95=>array(0,-126,500,-74),96=>array(19,507,242,678),97=>array(37,-10,442,460),98=>array(3,-10,468,683),99=>array(26,-10,412,460),100=>array(27,-10,491,683),101=>array(26,-10,424,460),102=>array(20,0,383,683),103=>array(28,-218,470,460),104=>array(9,0,487,683),105=>array(16,0,253,683),106=>array(-70,-218,194,683),107=>array(7,0,506,683),108=>array(19,0,257,683),109=>array(16,0,774,460),110=>array(16,0,486,460),111=>array(29,-10,470,460),112=>array(6,-217,470,460),113=>array(24,-217,488,461),114=>array(6,0,334,460),115=>array(51,-10,348,459),116=>array(13,-10,279,579),117=>array(9,-10,479,450),118=>array(19,-14,477,450),119=>array(21,-14,694,450),120=>array(17,0,479,450),121=>array(14,-218,474,450),122=>array(27,0,418,450),123=>array(100,-181,350,680),124=>array(67,-14,133,676),125=>array(130,-181,380,680),126=>array(40,186,502,320),160=>array(0,0,0,0),161=>array(97,-218,204,469),162=>array(53,-138,448,579),163=>array(12,-8,490,676),164=>array(-22,58,522,602),165=>array(-53,0,512,662),166=>array(67,-14,133,676),167=>array(70,-148,426,676),168=>array(18,523,316,622),169=>array(38,-14,722,676),170=>array(4,394,270,676),171=>array(42,33,456,416),172=>array(30,108,534,386),173=>array(39,194,286,257),174=>array(38,-14,722,676),175=>array(11,547,322,601),176=>array(57,390,343,676),177=>array(30,0,534,568),178=>array(1,270,296,676),179=>array(14,262,291,676),180=>array(93,507,317,678),181=>array(59,-224,514,451),182=>array(-22,-154,450,662),183=>array(70,199,181,310),184=>array(52,-214,261,0),185=>array(57,270,248,676),186=>array(6,394,304,676),187=>array(44,33,458,416),188=>array(37,-14,718,676),189=>array(31,-14,746,676),190=>array(14,-14,718,676),191=>array(30,-218,376,468),192=>array(14,0,706,890),193=>array(14,0,706,890),194=>array(14,0,706,886),195=>array(14,0,706,850),196=>array(14,0,706,836),197=>array(14,0,706,914),198=>array(0,0,863,662),199=>array(28,-214,633,676),200=>array(12,0,597,890),201=>array(12,0,597,890),202=>array(12,0,597,886),203=>array(12,0,597,834),204=>array(18,0,314,890),205=>array(18,0,317,890),206=>array(11,0,322,886),207=>array(18,0,316,836),208=>array(16,0,686,662),209=>array(12,-11,707,850),210=>array(34,-14,688,888),211=>array(34,-14,688,890),212=>array(34,-14,688,886),213=>array(34,-14,688,850),214=>array(34,-14,688,836),215=>array(38,8,527,497),216=>array(34,-80,688,734),217=>array(14,-14,706,890),218=>array(14,-14,706,890),219=>array(14,-14,706,886),220=>array(14,-14,706,834),221=>array(22,0,703,890),222=>array(16,0,542,662),223=>array(12,-9,468,683),224=>array(37,-10,442,678),225=>array(37,-10,442,678),226=>array(37,-10,442,674),227=>array(37,-10,442,638),228=>array(37,-10,442,619),229=>array(37,-10,442,718),230=>array(38,-10,632,460),231=>array(26,-214,412,460),232=>array(26,-10,424,678),233=>array(26,-10,424,678),234=>array(26,-10,424,674),235=>array(26,-10,424,621),236=>array(-8,0,253,678),237=>array(16,0,291,678),238=>array(-16,0,296,674),239=>array(11,0,269,622),240=>array(29,-10,471,686),241=>array(16,0,486,638),242=>array(29,-10,470,678),243=>array(29,-10,470,678),244=>array(29,-10,470,674),245=>array(29,-10,470,637),246=>array(29,-10,470,619),247=>array(30,-10,534,516),248=>array(29,-112,470,551),249=>array(9,-10,479,678),250=>array(9,-10,479,678),251=>array(9,-10,479,674),252=>array(9,-10,479,621),253=>array(14,-218,474,678),254=>array(6,-217,470,683),255=>array(14,-218,474,625),256=>array(14,0,706,791),257=>array(37,-10,442,574),258=>array(14,0,706,893),259=>array(37,-10,442,677),260=>array(14,-166,787,674),261=>array(37,-166,444,460),262=>array(28,-14,633,890),263=>array(26,-10,412,678),264=>array(28,-14,633,886),265=>array(26,-10,412,674),266=>array(28,-14,633,836),267=>array(26,-10,412,622),268=>array(28,-14,633,886),269=>array(26,-10,412,674),270=>array(16,0,686,887),271=>array(27,-10,599,683),272=>array(16,0,686,662),273=>array(27,-10,500,683),274=>array(12,0,597,791),275=>array(26,-10,424,574),276=>array(12,0,597,876),277=>array(26,-10,424,677),278=>array(12,0,597,836),279=>array(26,-10,424,622),280=>array(12,-166,613,662),281=>array(26,-166,424,460),282=>array(12,0,597,886),283=>array(26,-10,424,675),284=>array(32,-14,709,884),285=>array(28,-218,470,674),286=>array(32,-14,709,874),287=>array(28,-218,470,665),288=>array(32,-14,709,836),289=>array(28,-218,470,619),290=>array(32,-281,709,676),291=>array(28,-218,470,736),292=>array(19,0,702,903),293=>array(9,0,487,890),294=>array(19,0,702,662),295=>array(9,0,534,683),296=>array(1,0,331,842),297=>array(-28,0,301,626),298=>array(11,0,322,791),299=>array(-19,0,292,574),300=>array(18,0,314,893),301=>array(-4,0,277,677),302=>array(18,-166,397,662),303=>array(16,-166,278,683),304=>array(18,0,314,836),305=>array(16,0,253,460),306=>array(18,-14,703,662),307=>array(16,-218,472,683),308=>array(10,-14,382,903),309=>array(-51,-159,306,687),310=>array(34,-275,723,662),311=>array(7,-275,506,683),312=>array(7,0,490,460),313=>array(12,0,598,907),314=>array(19,0,294,894),315=>array(12,-275,598,662),316=>array(19,-275,257,683),317=>array(12,0,598,676),318=>array(19,0,348,683),319=>array(12,0,721,662),320=>array(19,0,415,683),321=>array(12,0,598,662),322=>array(19,0,259,683),323=>array(12,-11,707,892),324=>array(16,0,486,679),325=>array(12,-256,707,662),326=>array(16,-255,486,460),327=>array(12,-11,707,884),328=>array(16,0,486,675),329=>array(4,0,535,738),330=>array(12,-218,707,662),331=>array(16,-218,424,460),332=>array(34,-14,688,791),333=>array(29,-10,470,574),334=>array(34,-14,688,893),335=>array(29,-10,470,677),336=>array(34,-14,688,890),337=>array(29,-10,470,680),338=>array(30,-6,886,668),339=>array(30,-10,690,460),340=>array(17,0,659,907),341=>array(6,0,334,679),342=>array(17,-275,659,662),343=>array(6,-275,334,460),344=>array(17,0,659,886),345=>array(6,0,334,674),346=>array(42,-14,491,892),347=>array(51,-10,367,680),348=>array(42,-14,491,886),349=>array(37,-10,348,675),350=>array(42,-214,491,676),351=>array(51,-214,348,459),352=>array(42,-14,491,887),353=>array(38,-10,350,676),354=>array(17,-214,593,662),355=>array(13,-214,279,579),356=>array(17,0,593,888),357=>array(13,-10,300,676),358=>array(17,0,593,662),359=>array(13,-10,279,579),360=>array(14,-14,706,842),361=>array(9,-10,479,626),362=>array(14,-14,706,791),363=>array(9,-10,479,574),364=>array(14,-14,706,893),365=>array(9,-10,479,677),366=>array(14,-14,706,934),367=>array(9,-10,479,718),368=>array(14,-14,706,892),369=>array(9,-10,479,680),370=>array(14,-166,706,662),371=>array(9,-166,500,450),372=>array(6,-11,932,887),373=>array(21,-14,694,676),374=>array(22,0,703,887),375=>array(14,-218,474,675),376=>array(22,0,703,836),377=>array(9,0,597,891),378=>array(27,0,418,679),379=>array(9,0,597,836),380=>array(27,0,418,619),381=>array(9,0,597,886),382=>array(27,0,418,674),383=>array(20,0,383,683),384=>array(-1,-10,468,683),385=>array(-104,0,593,662),386=>array(39,0,608,686),387=>array(-10,-10,468,662),388=>array(3,0,604,683),389=>array(-17,-10,468,683),390=>array(28,-14,633,676),391=>array(28,-14,806,822),392=>array(26,-10,554,570),393=>array(16,0,686,662),394=>array(-100,0,686,662),395=>array(39,0,608,686),396=>array(27,-10,503,662),397=>array(31,-224,477,459),398=>array(12,0,597,662),399=>array(37,-14,688,676),400=>array(28,-14,484,702),401=>array(-67,-204,546,662),402=>array(-76,-218,384,683),403=>array(32,-14,816,822),404=>array(23,-14,643,662),405=>array(9,-9,709,683),406=>array(19,-10,326,683),407=>array(1,0,331,662),408=>array(34,0,794,664),409=>array(7,0,506,683),410=>array(1,0,281,683),411=>array(28,-10,458,672),412=>array(10,-10,931,662),413=>array(-70,-218,707,662),414=>array(16,-200,486,460),415=>array(30,-12,714,673),416=>array(34,-14,708,796),417=>array(29,-10,536,552),418=>array(34,-14,1022,676),419=>array(29,-217,774,460),420=>array(-106,0,542,662),421=>array(6,-217,470,683),422=>array(6,-113,659,662),423=>array(42,-14,491,676),424=>array(51,-10,348,459),425=>array(14,0,562,661),426=>array(22,-190,576,683),427=>array(13,-218,279,579),428=>array(-9,0,593,662),429=>array(-12,-10,350,683),430=>array(17,-204,593,662),431=>array(14,-14,826,848),432=>array(9,-10,538,636),433=>array(33,-13,713,663),434=>array(14,-14,688,662),435=>array(17,0,803,666),436=>array(14,-218,651,459),437=>array(9,0,597,662),438=>array(27,0,418,450),439=>array(14,-14,496,676),440=>array(14,-14,496,676),441=>array(6,-209,362,450),442=>array(46,-228,434,450),443=>array(30,0,474,676),444=>array(31,-14,580,662),445=>array(26,-183,404,477),446=>array(26,-10,379,579),447=>array(6,-217,472,460),448=>array(67,-14,133,676),449=>array(67,-14,333,676),450=>array(47,-108,553,614),451=>array(130,-9,237,676),452=>array(16,0,1319,886),453=>array(16,0,1140,674),454=>array(27,-10,918,683),455=>array(12,-14,981,662),456=>array(12,-218,804,683),457=>array(19,-218,472,683),458=>array(12,-14,1092,662),459=>array(12,-218,916,683),460=>array(16,-218,694,683),461=>array(14,0,706,886),462=>array(37,-10,442,674),463=>array(8,0,320,886),464=>array(-19,-2,292,672),465=>array(34,-14,688,886),466=>array(29,-10,470,674),467=>array(14,-14,706,882),468=>array(9,-10,479,674),469=>array(14,-14,706,913),470=>array(9,-10,479,721),471=>array(14,-14,706,931),472=>array(9,-10,479,788),473=>array(14,-14,706,963),474=>array(9,-10,479,784),475=>array(14,-14,706,939),476=>array(9,-10,479,788),477=>array(18,-10,417,460),478=>array(14,0,706,834),479=>array(37,-10,442,721),480=>array(14,0,706,913),481=>array(37,-10,442,721),482=>array(0,0,863,813),483=>array(38,-10,632,460),484=>array(32,-14,709,676),485=>array(11,-218,489,460),486=>array(32,-14,709,886),487=>array(28,-218,470,674),488=>array(34,0,723,886),489=>array(7,0,506,886),490=>array(34,-14,688,676),491=>array(29,-10,470,460),492=>array(34,-14,688,813),493=>array(29,-10,470,601),494=>array(14,-14,496,903),495=>array(6,-209,362,674),496=>array(-70,-218,292,672),497=>array(16,0,1319,662),498=>array(16,0,1140,662),499=>array(27,-10,918,683),500=>array(32,-14,709,676),501=>array(28,-218,470,678),502=>array(19,-14,917,662),504=>array(12,-11,707,890),505=>array(16,0,486,678),506=>array(14,0,706,914),507=>array(37,-10,442,718),508=>array(0,0,863,890),509=>array(38,-10,632,678),510=>array(34,-80,688,734),511=>array(29,-112,470,678),512=>array(14,0,706,890),513=>array(27,-10,442,678),514=>array(14,0,706,876),515=>array(37,-10,442,664),516=>array(12,0,597,890),517=>array(24,-10,424,678),518=>array(12,0,597,876),519=>array(26,-10,424,664),520=>array(-77,0,314,890),521=>array(-71,0,309,678),522=>array(18,0,314,876),523=>array(-4,0,277,664),524=>array(34,-14,688,890),525=>array(29,-10,470,678),526=>array(34,-14,688,876),527=>array(29,-10,470,664),528=>array(17,0,659,890),529=>array(-53,0,334,678),530=>array(17,0,659,876),531=>array(6,0,334,664),532=>array(14,-14,706,890),533=>array(9,-10,479,678),534=>array(14,-14,706,876),535=>array(9,-10,479,664),536=>array(42,-281,491,676),537=>array(51,-281,348,459),538=>array(17,-281,593,662),539=>array(13,-281,279,579),542=>array(19,0,702,886),543=>array(-6,0,487,886),550=>array(14,0,706,834),551=>array(37,-10,442,622),552=>array(12,-214,597,662),553=>array(26,-214,424,460),554=>array(34,-14,688,913),555=>array(29,-10,470,721),556=>array(34,-14,688,923),557=>array(29,-10,470,721),558=>array(34,-14,688,834),559=>array(29,-10,470,622),560=>array(34,-14,688,913),561=>array(29,-10,470,721),562=>array(22,0,703,813),563=>array(14,-218,474,601),565=>array(16,-230,578,460),567=>array(-70,-218,193,460),1548=>array(71,38,208,312),1563=>array(73,11,211,442),1567=>array(36,0,287,536),1569=>array(30,-59,264,213),1570=>array(21,-59,302,869),1571=>array(41,-58,259,760),1572=>array(59,-267,402,422),1573=>array(57,-292,288,738),1574=>array(42,-163,599,418),1575=>array(57,-58,259,738),1576=>array(38,-198,751,328),1577=>array(46,2,292,584),1578=>array(37,1,751,433),1579=>array(39,2,753,521),1580=>array(41,-440,563,411),1581=>array(34,-437,557,414),1582=>array(44,-437,567,553),1583=>array(51,1,364,454),1584=>array(43,0,357,647),1585=>array(48,-269,408,313),1586=>array(46,-269,406,513),1587=>array(36,-229,832,302),1588=>array(47,-225,844,551),1589=>array(46,-225,1032,304),1590=>array(35,-230,1022,434),1591=>array(41,0,698,738),1592=>array(40,-4,697,734),1593=>array(33,-438,462,420),1594=>array(34,-438,462,599),1600=>array(0,0,440,88),1601=>array(30,1,751,578),1602=>array(38,-228,534,454),1603=>array(37,1,744,739),1604=>array(43,-228,542,738),1605=>array(47,-412,354,242),1606=>array(46,-227,550,372),1607=>array(35,-2,282,411),1608=>array(39,-270,382,256),1609=>array(37,-164,593,417),1610=>array(44,-329,600,418),1611=>array(18,688,216,925),1612=>array(11,682,246,921),1613=>array(4,-501,202,-263),1614=>array(18,695,216,847),1615=>array(17,687,219,937),1616=>array(2,-427,200,-276),1617=>array(57,921,265,1114),1618=>array(6,731,158,914),1632=>array(157,109,292,284),1633=>array(143,-8,292,496),1634=>array(74,-8,358,496),1635=>array(46,-8,380,496),1636=>array(95,0,348,496),1637=>array(82,0,354,496),1638=>array(63,0,373,496),1639=>array(29,0,384,496),1640=>array(31,0,386,496),1641=>array(74,0,354,496),1642=>array(33,0,428,664),1645=>array(19,109,368,441),7680=>array(14,-238,706,674),7681=>array(37,-249,442,460),7682=>array(17,0,593,801),7683=>array(3,-10,468,825),7684=>array(17,-139,593,662),7685=>array(3,-149,468,683),7686=>array(17,-94,593,662),7687=>array(3,-104,468,683),7688=>array(28,-214,633,897),7689=>array(26,-214,412,661),7690=>array(16,0,686,801),7691=>array(27,-10,491,683),7692=>array(16,-139,686,662),7693=>array(27,-149,491,683),7694=>array(16,-94,686,662),7695=>array(27,-104,491,683),7696=>array(16,-214,686,662),7697=>array(27,-224,491,683),7698=>array(16,-219,686,662),7699=>array(27,-229,491,683),7700=>array(12,0,597,1024),7701=>array(26,-10,424,812),7702=>array(12,0,597,1024),7703=>array(26,-10,424,812),7704=>array(12,-219,597,662),7705=>array(26,-229,424,460),7706=>array(12,-146,597,662),7707=>array(26,-156,424,460),7708=>array(12,-214,597,858),7709=>array(26,-214,424,647),7710=>array(12,0,546,801),7711=>array(20,0,383,825),7712=>array(32,-14,709,780),7713=>array(28,-218,470,544),7714=>array(19,0,702,801),7715=>array(9,0,487,825),7716=>array(19,-139,702,662),7717=>array(9,-139,487,683),7718=>array(19,0,702,801),7719=>array(9,0,487,825),7720=>array(19,-214,702,662),7721=>array(9,-214,487,683),7722=>array(19,-197,702,662),7723=>array(9,-197,487,683),7724=>array(1,-146,331,662),7725=>array(-31,-146,298,683),7726=>array(18,0,316,1044),7727=>array(11,0,269,832),7728=>array(34,0,723,872),7729=>array(7,0,506,897),7730=>array(34,-139,723,662),7731=>array(7,-139,506,683),7732=>array(34,-94,723,662),7733=>array(7,-94,506,683),7734=>array(12,-139,598,662),7735=>array(19,-139,257,683),7736=>array(12,-139,598,756),7737=>array(-17,-139,294,780),7738=>array(12,-94,598,662),7739=>array(-17,-94,294,683),7740=>array(12,-219,598,662),7741=>array(-17,-219,294,683),7742=>array(12,0,863,872),7743=>array(16,0,774,661),7744=>array(12,0,863,801),7745=>array(16,0,774,589),7746=>array(12,-139,863,662),7747=>array(16,-139,774,460),7748=>array(12,-11,707,801),7749=>array(16,0,486,589),7750=>array(12,-151,707,662),7751=>array(16,-139,486,460),7752=>array(12,-106,707,662),7753=>array(16,-94,486,460),7754=>array(12,-231,707,662),7755=>array(16,-219,486,460),7756=>array(34,-14,688,1061),7757=>array(29,-10,470,848),7758=>array(34,-14,688,989),7759=>array(29,-10,470,777),7760=>array(34,-14,688,1024),7761=>array(29,-10,470,812),7762=>array(34,-14,688,1024),7763=>array(29,-10,470,812),7764=>array(16,0,542,872),7765=>array(6,-217,470,661),7766=>array(16,0,542,801),7767=>array(6,-217,470,589),7768=>array(17,0,659,801),7769=>array(6,0,334,589),7770=>array(17,-139,659,662),7771=>array(6,-139,334,460),7772=>array(17,-139,659,756),7773=>array(6,-139,334,544),7774=>array(17,-94,659,662),7775=>array(6,-94,334,460),7776=>array(42,-14,491,825),7777=>array(51,-10,348,589),7778=>array(42,-153,491,676),7779=>array(51,-149,348,459),7780=>array(42,-14,491,1029),7781=>array(51,-10,367,825),7782=>array(42,-14,491,1025),7783=>array(38,-10,350,825),7784=>array(42,-153,491,825),7785=>array(51,-149,348,589),7786=>array(17,0,593,801),7787=>array(13,-10,279,718),7788=>array(17,-139,593,662),7789=>array(13,-149,279,579),7790=>array(17,-94,593,662),7791=>array(-9,-104,302,579),7792=>array(17,-219,593,662),7793=>array(-9,-229,302,579),7794=>array(14,-153,706,662),7795=>array(9,-149,479,450),7796=>array(14,-160,706,662),7797=>array(9,-156,479,450),7798=>array(14,-233,706,662),7799=>array(9,-229,479,450),7800=>array(14,-14,706,1061),7801=>array(9,-10,479,848),7802=>array(14,-14,706,952),7803=>array(9,-10,479,740),7804=>array(16,-11,697,808),7805=>array(19,-14,477,596),7806=>array(16,-151,697,662),7807=>array(19,-153,477,450),7808=>array(6,-11,932,872),7809=>array(21,-14,694,661),7810=>array(6,-11,932,872),7811=>array(21,-14,694,661),7812=>array(6,-11,932,801),7813=>array(21,-14,694,589),7814=>array(6,-11,932,801),7815=>array(21,-14,694,589),7816=>array(6,-151,932,662),7817=>array(21,-153,694,450),7818=>array(10,0,704,801),7819=>array(17,0,479,589),7820=>array(10,0,704,801),7821=>array(17,0,479,589),7822=>array(22,0,703,801),7823=>array(14,-218,474,589),7824=>array(9,0,597,868),7825=>array(27,0,418,657),7826=>array(9,-139,597,662),7827=>array(27,-139,418,450),7828=>array(9,-94,597,662),7829=>array(27,-94,418,450),7830=>array(9,-94,487,683),7831=>array(-3,-10,295,718),7832=>array(21,-14,694,688),7833=>array(14,-218,474,688),7834=>array(37,-10,442,710),7835=>array(20,0,383,825),7840=>array(14,-139,706,674),7841=>array(37,-149,442,460),7842=>array(14,0,706,920),7843=>array(37,-10,442,708),7844=>array(14,0,706,1097),7845=>array(37,-10,442,897),7846=>array(14,0,706,1097),7847=>array(37,-10,442,897),7848=>array(14,0,706,911),7849=>array(37,-10,498,696),7850=>array(14,0,706,1032),7851=>array(37,-10,442,832),7852=>array(14,-139,706,893),7853=>array(37,-149,442,657),7854=>array(14,0,706,1087),7855=>array(37,-10,442,875),7856=>array(14,0,706,1087),7857=>array(37,-10,442,875),7858=>array(14,0,706,1040),7859=>array(37,-10,442,838),7860=>array(14,0,706,1022),7861=>array(37,-10,442,810),7862=>array(14,-139,706,883),7863=>array(37,-149,442,647),7864=>array(12,-139,597,662),7865=>array(26,-149,424,460),7866=>array(12,0,597,920),7867=>array(26,-10,424,708),7868=>array(12,0,597,808),7869=>array(26,-10,424,596),7870=>array(12,0,597,1097),7871=>array(26,-10,424,897),7872=>array(12,0,597,1097),7873=>array(26,-10,424,897),7874=>array(12,0,608,920),7875=>array(26,-10,504,708),7876=>array(12,0,597,1032),7877=>array(26,-10,424,832),7878=>array(12,-139,597,868),7879=>array(26,-149,424,657),7880=>array(-240,0,314,911),7881=>array(-272,0,253,699),7882=>array(18,-139,314,662),7883=>array(16,-139,253,683),7884=>array(34,-153,688,676),7885=>array(29,-149,470,460),7886=>array(34,-14,688,924),7887=>array(29,-10,470,706),7888=>array(34,-14,688,1097),7889=>array(29,-10,470,897),7890=>array(34,-14,688,1097),7891=>array(29,-10,470,897),7892=>array(34,-14,688,924),7893=>array(29,-10,537,706),7894=>array(34,-14,688,1032),7895=>array(29,-10,470,832),7896=>array(34,-153,688,893),7897=>array(29,-149,470,657),7898=>array(34,-14,708,895),7899=>array(29,-10,536,679),7900=>array(34,-14,708,904),7901=>array(29,-10,536,677),7902=>array(34,-14,708,924),7903=>array(29,-10,536,706),7904=>array(34,-14,708,857),7905=>array(29,-10,536,618),7906=>array(34,-173,708,796),7907=>array(29,-169,536,552),7908=>array(14,-153,706,662),7909=>array(9,-149,479,450),7910=>array(14,-14,706,920),7911=>array(9,-10,479,708),7912=>array(14,-14,826,914),7913=>array(9,-10,538,684),7914=>array(14,-14,826,908),7915=>array(9,-10,538,678),7916=>array(14,-14,826,920),7917=>array(9,-10,538,708),7918=>array(14,-14,826,848),7919=>array(9,-10,538,640),7920=>array(14,-173,826,848),7921=>array(9,-169,538,636),7922=>array(22,0,703,872),7923=>array(14,-218,474,661),7924=>array(22,-139,703,662),7925=>array(14,-357,474,450),7926=>array(22,0,703,920),7927=>array(14,-218,474,708),7928=>array(22,0,703,808),7929=>array(14,-218,474,596),8204=>array(-17,0,17,910),8205=>array(-110,0,110,910),8206=>array(-123,0,123,916),8207=>array(-123,0,123,916),8234=>array(-123,-94,123,822),8235=>array(-123,-94,123,822),8236=>array(-111,0,111,910),8237=>array(-123,-94,123,916),8238=>array(-123,-94,123,916),64256=>array(26,0,479,500),64257=>array(22,0,382,500),64258=>array(23,0,382,500),64259=>array(26,0,583,500),64260=>array(26,0,582,500),64262=>array(37,-7,494,512),64830=>array(29,-259,520,689),64831=>array(34,-259,525,689),65010=>array(46,1,664,673),65152=>array(49,-58,283,215),65153=>array(32,-59,314,869),65154=>array(28,0,391,870),65155=>array(41,-58,259,760),65156=>array(41,0,340,760),65157=>array(51,-267,394,422),65158=>array(48,-267,448,422),65159=>array(57,-292,288,738),65160=>array(57,-232,354,738),65161=>array(47,-164,603,417),65162=>array(34,-262,538,202),65163=>array(0,0,214,534),65164=>array(0,0,253,523),65165=>array(57,-58,259,738),65166=>array(57,0,354,738),65167=>array(34,-196,748,331),65168=>array(29,-196,777,330),65169=>array(0,-197,214,329),65170=>array(0,-197,253,329),65171=>array(34,2,281,583),65172=>array(36,1,438,582),65173=>array(42,0,756,432),65174=>array(37,0,784,432),65175=>array(0,0,244,515),65176=>array(0,0,296,515),65177=>array(39,1,753,518),65178=>array(37,0,784,518),65179=>array(0,0,244,601),65180=>array(0,0,293,601),65181=>array(38,-437,561,414),65182=>array(46,-438,568,413),65183=>array(0,-196,551,366),65184=>array(0,-196,539,366),65185=>array(42,-438,564,413),65186=>array(46,-438,568,413),65187=>array(0,0,551,366),65188=>array(0,0,539,366),65189=>array(38,-436,561,554),65190=>array(41,-438,563,551),65191=>array(0,0,551,588),65192=>array(0,0,539,515),65193=>array(47,0,361,454),65194=>array(53,0,401,454),65195=>array(50,0,364,647),65196=>array(48,-1,397,647),65197=>array(47,-268,407,314),65198=>array(38,-267,464,317),65199=>array(49,-272,409,511),65200=>array(43,-267,469,516),65201=>array(38,-228,834,303),65202=>array(43,-228,917,303),65203=>array(0,0,563,303),65204=>array(0,0,640,303),65205=>array(36,-228,832,548),65206=>array(41,-228,914,549),65207=>array(0,0,563,555),65208=>array(0,0,640,555),65209=>array(46,-228,1032,302),65210=>array(41,-228,1085,302),65211=>array(0,0,708,302),65212=>array(0,0,761,302),65213=>array(38,-228,1025,432),65214=>array(41,-228,1085,432),65215=>array(0,0,708,432),65216=>array(0,0,761,432),65217=>array(50,-1,707,738),65218=>array(49,0,729,738),65219=>array(0,0,593,738),65220=>array(0,0,616,738),65221=>array(47,0,704,738),65222=>array(42,0,722,738),65223=>array(0,0,593,738),65224=>array(0,0,616,738),65225=>array(41,-438,469,419),65226=>array(63,-458,496,274),65227=>array(0,0,493,362),65228=>array(0,0,388,281),65229=>array(39,-438,467,599),65230=>array(63,-458,496,516),65231=>array(0,0,493,542),65232=>array(0,0,388,516),65233=>array(42,0,763,578),65234=>array(35,1,792,458),65235=>array(0,0,298,578),65236=>array(0,0,316,458),65237=>array(41,-228,537,454),65238=>array(41,-228,584,454),65239=>array(0,0,298,599),65240=>array(0,0,316,491),65241=>array(38,1,746,739),65242=>array(0,0,768,738),65243=>array(0,0,357,762),65244=>array(0,0,410,762),65245=>array(38,-228,537,738),65246=>array(46,-228,616,738),65247=>array(0,0,220,738),65248=>array(0,0,245,738),65249=>array(48,-413,356,240),65250=>array(48,-416,472,256),65251=>array(0,0,385,256),65252=>array(0,-14,424,256),65253=>array(54,-231,559,367),65254=>array(38,-228,616,370),65255=>array(0,0,214,516),65256=>array(0,0,253,516),65257=>array(35,-2,282,411),65258=>array(42,1,445,413),65259=>array(0,-16,452,522),65260=>array(0,-173,415,373),65261=>array(47,-268,390,259),65262=>array(50,-267,449,259),65263=>array(41,-163,597,419),65264=>array(42,-262,546,182),65265=>array(43,-333,599,414),65266=>array(42,-422,546,183),65267=>array(0,-236,244,329),65268=>array(0,-236,283,329),65269=>array(39,-88,507,752),65270=>array(39,-99,744,779),65271=>array(52,-88,507,792),65272=>array(57,-99,744,788),65273=>array(74,-292,507,749),65274=>array(68,-248,744,736),65275=>array(48,-89,481,748),65276=>array(68,-99,744,736),65535=>array(33,0,300,667));
$cw=array(0=>0,12=>318,32=>250,33=>332,34=>408,35=>500,36=>500,37=>832,38=>778,39=>180,40=>332,41=>332,42=>500,43=>564,44=>250,45=>332,46=>250,47=>278,48=>500,49=>500,50=>500,51=>500,52=>500,53=>500,54=>500,55=>500,56=>500,57=>500,58=>278,59=>278,60=>564,61=>564,62=>564,63=>444,64=>921,65=>722,66=>667,67=>667,68=>722,69=>611,70=>556,71=>722,72=>722,73=>332,74=>388,75=>722,76=>611,77=>888,78=>722,79=>722,80=>556,81=>722,82=>667,83=>556,84=>611,85=>722,86=>722,87=>944,88=>722,89=>722,90=>611,91=>332,92=>278,93=>332,94=>468,95=>500,96=>332,97=>444,98=>500,99=>444,100=>500,101=>444,102=>332,103=>500,104=>500,105=>278,106=>278,107=>500,108=>278,109=>778,110=>500,111=>500,112=>500,113=>500,114=>332,115=>388,116=>278,117=>500,118=>500,119=>722,120=>500,121=>500,122=>444,123=>480,124=>200,125=>480,126=>541,160=>250,161=>332,162=>500,163=>500,164=>500,165=>500,166=>200,167=>500,168=>332,169=>760,170=>276,171=>500,172=>564,173=>332,174=>760,175=>332,176=>400,177=>564,178=>300,179=>300,180=>332,181=>534,182=>452,183=>250,184=>332,185=>300,186=>310,187=>500,188=>750,189=>750,190=>750,191=>444,192=>722,193=>722,194=>722,195=>722,196=>722,197=>722,198=>888,199=>667,200=>611,201=>611,202=>611,203=>611,204=>332,205=>332,206=>332,207=>332,208=>722,209=>722,210=>722,211=>722,212=>722,213=>722,214=>722,215=>564,216=>722,217=>722,218=>722,219=>722,220=>722,221=>722,222=>556,223=>500,224=>444,225=>444,226=>444,227=>444,228=>444,229=>444,230=>667,231=>444,232=>444,233=>444,234=>444,235=>444,236=>278,237=>278,238=>278,239=>278,240=>500,241=>500,242=>500,243=>500,244=>500,245=>500,246=>500,247=>564,248=>500,249=>500,250=>500,251=>500,252=>500,253=>500,254=>500,255=>500,256=>722,257=>444,258=>722,259=>444,260=>722,261=>444,262=>667,263=>444,264=>667,265=>444,266=>667,267=>444,268=>667,269=>444,270=>722,271=>600,272=>722,273=>500,274=>611,275=>444,276=>611,277=>444,278=>611,279=>444,280=>611,281=>444,282=>611,283=>444,284=>722,285=>500,286=>722,287=>500,288=>722,289=>500,290=>722,291=>500,292=>722,293=>500,294=>722,295=>548,296=>332,297=>278,298=>332,299=>278,300=>332,301=>278,302=>332,303=>278,304=>332,305=>278,306=>722,307=>556,308=>388,309=>203,310=>722,311=>500,312=>500,313=>611,314=>278,315=>611,316=>278,317=>611,318=>348,319=>611,320=>444,321=>611,322=>278,323=>722,324=>500,325=>722,326=>500,327=>722,328=>500,329=>556,330=>722,331=>500,332=>722,333=>500,334=>722,335=>500,336=>722,337=>500,338=>888,339=>722,340=>667,341=>332,342=>667,343=>332,344=>667,345=>332,346=>556,347=>388,348=>556,349=>388,350=>556,351=>388,352=>556,353=>388,354=>611,355=>278,356=>611,357=>278,358=>611,359=>278,360=>722,361=>500,362=>722,363=>500,364=>722,365=>500,366=>722,367=>500,368=>722,369=>500,370=>722,371=>500,372=>944,373=>722,374=>722,375=>500,376=>722,377=>611,378=>444,379=>611,380=>444,381=>611,382=>444,383=>332,384=>500,385=>667,386=>646,387=>500,388=>646,389=>500,390=>667,391=>667,392=>444,393=>722,394=>722,395=>646,396=>500,397=>534,398=>611,399=>722,400=>518,401=>556,402=>332,403=>722,404=>664,405=>728,406=>332,407=>332,408=>810,409=>500,410=>278,411=>480,412=>944,413=>722,414=>500,415=>726,416=>722,417=>500,418=>1042,419=>778,420=>556,421=>500,422=>667,423=>556,424=>388,425=>627,426=>592,427=>278,428=>611,429=>278,430=>611,431=>722,432=>500,433=>757,434=>722,435=>822,436=>667,437=>611,438=>444,439=>556,440=>556,441=>388,442=>500,443=>500,444=>614,445=>438,446=>421,447=>500,448=>200,449=>400,450=>600,451=>332,452=>1332,453=>1166,454=>944,455=>1000,456=>888,457=>556,458=>1111,459=>1000,460=>778,461=>722,462=>444,463=>332,464=>278,465=>722,466=>500,467=>722,468=>500,469=>722,470=>500,471=>722,472=>500,473=>722,474=>500,475=>722,476=>500,477=>444,478=>722,479=>444,480=>722,481=>444,482=>888,483=>667,484=>722,485=>500,486=>722,487=>500,488=>722,489=>500,490=>722,491=>500,492=>722,493=>500,494=>556,495=>388,496=>278,497=>1332,498=>1166,499=>944,500=>722,501=>500,502=>944,504=>722,505=>500,506=>722,507=>444,508=>888,509=>667,510=>722,511=>500,512=>722,513=>444,514=>722,515=>444,516=>611,517=>444,518=>611,519=>444,520=>332,521=>278,522=>332,523=>278,524=>722,525=>500,526=>722,527=>500,528=>667,529=>332,530=>667,531=>332,532=>722,533=>500,534=>722,535=>500,536=>556,537=>388,538=>611,539=>278,542=>722,543=>500,550=>722,551=>444,552=>611,553=>444,554=>722,555=>500,556=>722,557=>500,558=>722,559=>500,560=>722,561=>500,562=>722,563=>500,565=>597,567=>278,1548=>278,1563=>278,1567=>322,1569=>278,1570=>343,1571=>298,1572=>442,1573=>343,1574=>634,1575=>298,1576=>806,1577=>324,1578=>808,1579=>815,1580=>616,1581=>616,1582=>625,1583=>407,1584=>408,1585=>459,1586=>469,1587=>880,1588=>907,1589=>1099,1590=>1077,1591=>750,1592=>755,1593=>522,1594=>529,1600=>439,1601=>806,1602=>584,1603=>782,1604=>589,1605=>413,1606=>623,1607=>332,1608=>435,1609=>634,1610=>634,1611=>0,1612=>0,1613=>0,1614=>0,1615=>0,1616=>0,1617=>0,1618=>0,1632=>419,1633=>419,1634=>419,1635=>419,1636=>419,1637=>419,1638=>419,1639=>419,1640=>419,1641=>419,1642=>462,1645=>398,7680=>722,7681=>444,7682=>667,7683=>500,7684=>667,7685=>500,7686=>667,7687=>500,7688=>667,7689=>444,7690=>722,7691=>500,7692=>722,7693=>500,7694=>722,7695=>500,7696=>722,7697=>500,7698=>722,7699=>500,7700=>611,7701=>444,7702=>611,7703=>444,7704=>611,7705=>444,7706=>611,7707=>444,7708=>611,7709=>444,7710=>556,7711=>332,7712=>722,7713=>500,7714=>722,7715=>500,7716=>722,7717=>500,7718=>722,7719=>500,7720=>722,7721=>500,7722=>722,7723=>500,7724=>332,7725=>278,7726=>332,7727=>278,7728=>722,7729=>500,7730=>722,7731=>500,7732=>722,7733=>500,7734=>611,7735=>278,7736=>611,7737=>278,7738=>611,7739=>278,7740=>611,7741=>278,7742=>888,7743=>778,7744=>888,7745=>778,7746=>888,7747=>778,7748=>722,7749=>500,7750=>722,7751=>500,7752=>722,7753=>500,7754=>722,7755=>500,7756=>722,7757=>500,7758=>722,7759=>500,7760=>722,7761=>500,7762=>722,7763=>500,7764=>556,7765=>500,7766=>556,7767=>500,7768=>667,7769=>332,7770=>667,7771=>332,7772=>667,7773=>332,7774=>667,7775=>332,7776=>556,7777=>388,7778=>556,7779=>388,7780=>556,7781=>388,7782=>556,7783=>388,7784=>556,7785=>388,7786=>611,7787=>278,7788=>611,7789=>278,7790=>611,7791=>278,7792=>611,7793=>278,7794=>722,7795=>500,7796=>722,7797=>500,7798=>722,7799=>500,7800=>722,7801=>500,7802=>722,7803=>500,7804=>722,7805=>500,7806=>722,7807=>500,7808=>944,7809=>722,7810=>944,7811=>722,7812=>944,7813=>722,7814=>944,7815=>722,7816=>944,7817=>722,7818=>722,7819=>500,7820=>722,7821=>500,7822=>722,7823=>500,7824=>611,7825=>444,7826=>611,7827=>444,7828=>611,7829=>444,7830=>500,7831=>278,7832=>722,7833=>500,7834=>444,7835=>332,7840=>722,7841=>444,7842=>807,7843=>528,7844=>722,7845=>444,7846=>722,7847=>444,7848=>807,7849=>528,7850=>722,7851=>444,7852=>722,7853=>444,7854=>722,7855=>444,7856=>722,7857=>444,7858=>807,7859=>528,7860=>722,7861=>444,7862=>722,7863=>444,7864=>611,7865=>444,7866=>696,7867=>528,7868=>611,7869=>444,7870=>611,7871=>444,7872=>611,7873=>444,7874=>696,7875=>528,7876=>611,7877=>444,7878=>611,7879=>444,7880=>332,7881=>278,7882=>332,7883=>278,7884=>722,7885=>500,7886=>807,7887=>584,7888=>722,7889=>500,7890=>722,7891=>500,7892=>807,7893=>584,7894=>722,7895=>500,7896=>722,7897=>500,7898=>722,7899=>500,7900=>722,7901=>500,7902=>807,7903=>584,7904=>722,7905=>500,7906=>722,7907=>500,7908=>722,7909=>500,7910=>807,7911=>584,7912=>722,7913=>500,7914=>722,7915=>500,7916=>807,7917=>584,7918=>722,7919=>500,7920=>722,7921=>500,7922=>722,7923=>500,7924=>722,7925=>500,7926=>807,7927=>584,7928=>722,7929=>500,8204=>0,8205=>0,8206=>0,8207=>0,8234=>0,8235=>0,8236=>0,8237=>0,8238=>0,64256=>443,64257=>407,64258=>407,64259=>603,64260=>601,64262=>496,64830=>554,64831=>554,65010=>719,65152=>329,65153=>366,65154=>391,65155=>296,65156=>339,65157=>439,65158=>448,65159=>342,65160=>354,65161=>659,65162=>538,65163=>267,65164=>253,65165=>298,65166=>354,65167=>808,65168=>777,65169=>274,65170=>253,65171=>337,65172=>438,65173=>818,65174=>784,65175=>293,65176=>296,65177=>818,65178=>784,65179=>293,65180=>293,65181=>608,65182=>568,65183=>599,65184=>539,65185=>606,65186=>568,65187=>606,65188=>539,65189=>616,65190=>563,65191=>599,65192=>539,65193=>411,65194=>402,65195=>423,65196=>396,65197=>459,65198=>464,65199=>464,65200=>469,65201=>887,65202=>916,65203=>623,65204=>640,65205=>883,65206=>914,65207=>621,65208=>640,65209=>1080,65210=>1084,65211=>772,65212=>761,65213=>1075,65214=>1084,65215=>767,65216=>761,65217=>762,65218=>729,65219=>654,65220=>616,65221=>762,65222=>722,65223=>642,65224=>616,65225=>522,65226=>422,65227=>553,65228=>387,65229=>529,65230=>422,65231=>541,65232=>387,65233=>815,65234=>791,65235=>346,65236=>315,65237=>587,65238=>584,65239=>348,65240=>315,65241=>806,65242=>767,65243=>408,65244=>410,65245=>589,65246=>616,65247=>278,65248=>245,65249=>407,65250=>472,65251=>439,65252=>423,65253=>611,65254=>616,65255=>281,65256=>253,65257=>332,65258=>444,65259=>507,65260=>415,65261=>439,65262=>449,65263=>637,65264=>546,65265=>639,65266=>546,65267=>293,65268=>282,65269=>569,65270=>744,65271=>577,65272=>744,65273=>579,65274=>744,65275=>537,65276=>744,65535=>367);
// --- EOF ---
