/*
	jQuery Colorbox language configuration
	language: Hungarian (hu)
	translated by: k<PERSON><PERSON>i
*/
jQuery.extend(jQuery.colorbox.settings, {
	current: "{current}/{total} kép",
	previous: "<PERSON><PERSON><PERSON><PERSON>",
	next: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
	close: "<PERSON><PERSON><PERSON><PERSON>",
	xhrError: "A tartalmat nem sikerült betölteni.",
	imgError: "A képet nem sikerült betölteni.",
	slideshowStart: "Diavetítés indítása",
	slideshowStop: "Diavetítés leállítása"
});