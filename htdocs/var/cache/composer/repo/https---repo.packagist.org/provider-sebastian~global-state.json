{"minified": "composer/2.0", "packages": {"sebastian/global-state": [{"name": "sebastian/global-state", "description": "Snapshotting of global state", "keywords": ["global state"], "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "version": "8.0.2", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "ef1377171613d09edd25b7816f05be8313f9115d"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/ef1377171613d09edd25b7816f05be8313f9115d", "type": "zip", "shasum": "", "reference": "ef1377171613d09edd25b7816f05be8313f9115d"}, "type": "library", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/8.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/global-state", "type": "tidelift"}], "time": "2025-08-29T11:29:25+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "8.0-dev"}}, "require": {"php": ">=8.3", "sebastian/object-reflector": "^5.0", "sebastian/recursion-context": "^7.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^12.0"}}, {"version": "8.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "912dd568677a6e13c67c08321710ad6ac81e6dca"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/912dd568677a6e13c67c08321710ad6ac81e6dca", "type": "zip", "shasum": "", "reference": "912dd568677a6e13c67c08321710ad6ac81e6dca"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/8.0.1"}, "time": "2025-08-28T09:13:48+00:00"}, {"version": "8.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "570a2aeb26d40f057af686d63c4e99b075fb6cbc"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/570a2aeb26d40f057af686d63c4e99b075fb6cbc", "type": "zip", "shasum": "", "reference": "570a2aeb26d40f057af686d63c4e99b075fb6cbc"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/8.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:56:59+00:00"}, {"version": "7.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "3be331570a721f9a4b5917f4209773de17f747d7"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/3be331570a721f9a4b5917f4209773de17f747d7", "type": "zip", "shasum": "", "reference": "3be331570a721f9a4b5917f4209773de17f747d7"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/7.0.2"}, "time": "2024-07-03T04:57:36+00:00", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "require": {"php": ">=8.2", "sebastian/object-reflector": "^4.0", "sebastian/recursion-context": "^6.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^11.0"}}, {"version": "7.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "c3a307e832f2e69c7ef869e31fc644fde0e7cb3e"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/c3a307e832f2e69c7ef869e31fc644fde0e7cb3e", "type": "zip", "shasum": "", "reference": "c3a307e832f2e69c7ef869e31fc644fde0e7cb3e"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/7.0.1"}, "time": "2024-03-02T07:32:10+00:00"}, {"version": "7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "590e7cbc6565fa2e26c3df4e629a34bb0bc00c17"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/590e7cbc6565fa2e26c3df4e629a34bb0bc00c17", "type": "zip", "shasum": "", "reference": "590e7cbc6565fa2e26c3df4e629a34bb0bc00c17"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/7.0.0"}, "time": "2024-02-02T05:59:33+00:00"}, {"version": "6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "type": "zip", "shasum": "", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/6.0.2"}, "time": "2024-03-02T07:19:19+00:00", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^10.0"}}, {"homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "version": "6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "7ea9ead78f6d380d2a667864c132c2f7b83055e4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/7ea9ead78f6d380d2a667864c132c2f7b83055e4", "type": "zip", "shasum": "", "reference": "7ea9ead78f6d380d2a667864c132c2f7b83055e4"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/6.0.1"}, "time": "2023-07-19T07:19:23+00:00"}, {"version": "6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "aab257c712de87b90194febd52e4d184551c2d44"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/aab257c712de87b90194febd52e4d184551c2d44", "type": "zip", "shasum": "", "reference": "aab257c712de87b90194febd52e4d184551c2d44"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/6.0.0"}, "time": "2023-02-03T07:07:38+00:00"}, {"version": "5.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "b6781316bdcd28260904e7cc18ec983d0d2ef4f6"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/b6781316bdcd28260904e7cc18ec983d0d2ef4f6", "type": "zip", "shasum": "", "reference": "b6781316bdcd28260904e7cc18ec983d0d2ef4f6"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/global-state", "type": "tidelift"}], "time": "2025-08-10T07:10:35+00:00", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-uopz": "*"}}, {"version": "5.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "type": "zip", "shasum": "", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.7"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:35:11+00:00"}, {"version": "5.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "bde739e7565280bda77be70044ac1047bc007e34"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bde739e7565280bda77be70044ac1047bc007e34", "type": "zip", "shasum": "", "reference": "bde739e7565280bda77be70044ac1047bc007e34"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.6"}, "time": "2023-08-02T09:26:13+00:00"}, {"version": "5.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "0ca8db5a5fc9c8646244e629625ac486fa286bf2"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/0ca8db5a5fc9c8646244e629625ac486fa286bf2", "type": "zip", "shasum": "", "reference": "0ca8db5a5fc9c8646244e629625ac486fa286bf2"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.5"}, "time": "2022-02-14T08:28:10+00:00"}, {"version": "5.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "19c519631c5a511b7ed0ad64a6713fdb3fd25fe4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/19c519631c5a511b7ed0ad64a6713fdb3fd25fe4", "type": "zip", "shasum": "", "reference": "19c519631c5a511b7ed0ad64a6713fdb3fd25fe4"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.4"}, "time": "2022-02-10T07:01:19+00:00"}, {"version": "5.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "23bd5951f7ff26f12d4e3242864df3e08dec4e49"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/23bd5951f7ff26f12d4e3242864df3e08dec4e49", "type": "zip", "shasum": "", "reference": "23bd5951f7ff26f12d4e3242864df3e08dec4e49"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.3"}, "time": "2021-06-11T13:31:12+00:00"}, {"version": "5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "a90ccbddffa067b51f574dea6eb25d5680839455"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/a90ccbddffa067b51f574dea6eb25d5680839455", "type": "zip", "shasum": "", "reference": "a90ccbddffa067b51f574dea6eb25d5680839455"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.2"}, "time": "2020-10-26T15:55:19+00:00"}, {"version": "5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "ea779cb749a478b22a2564ac41cd7bda79c78dc7"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/ea779cb749a478b22a2564ac41cd7bda79c78dc7", "type": "zip", "shasum": "", "reference": "ea779cb749a478b22a2564ac41cd7bda79c78dc7"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.1"}, "time": "2020-09-28T05:54:06+00:00"}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "22ae663c951bdc39da96603edc3239ed3a299097"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/22ae663c951bdc39da96603edc3239ed3a299097", "type": "zip", "shasum": "", "reference": "22ae663c951bdc39da96603edc3239ed3a299097"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.0"}, "time": "2020-08-07T04:09:03+00:00", "require": {"php": "^7.3 || ^8.0", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "bdb1e7c79e592b8c82cb1699be3c8743119b8a72"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bdb1e7c79e592b8c82cb1699be3c8743119b8a72", "type": "zip", "shasum": "", "reference": "bdb1e7c79e592b8c82cb1699be3c8743119b8a72"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/global-state/tree/master"}, "time": "2020-02-07T06:11:37+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "require": {"php": "^7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.0"}, "funding": "__unset"}, {"version": "3.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "800689427e3e8cf57a8fe38fcd1d4344c9b2f046"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/800689427e3e8cf57a8fe38fcd1d4344c9b2f046", "type": "zip", "shasum": "", "reference": "800689427e3e8cf57a8fe38fcd1d4344c9b2f046"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/global-state", "type": "tidelift"}], "time": "2025-08-10T05:40:12+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "require": {"php": ">=7.2", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^8.0"}}, {"version": "3.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "91c7c47047a971f02de57ed6f040087ef110c5d9"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/91c7c47047a971f02de57ed6f040087ef110c5d9", "type": "zip", "shasum": "", "reference": "91c7c47047a971f02de57ed6f040087ef110c5d9"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/3.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:13:16+00:00"}, {"version": "3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "3b7bb09ded5565e87da9357f423ec4b69c4f4e06"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/3b7bb09ded5565e87da9357f423ec4b69c4f4e06", "type": "zip", "shasum": "", "reference": "3b7bb09ded5565e87da9357f423ec4b69c4f4e06"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/3.0.4"}, "time": "2024-03-01T13:52:17+00:00"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "66783ce213de415b451b904bfef9dda0cf9aeae0"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/66783ce213de415b451b904bfef9dda0cf9aeae0", "type": "zip", "shasum": "", "reference": "66783ce213de415b451b904bfef9dda0cf9aeae0"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/3.0.3"}, "time": "2023-08-02T09:23:32+00:00"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "de036ec91d55d2a9e0db2ba975b512cdb1c23921"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/de036ec91d55d2a9e0db2ba975b512cdb1c23921", "type": "zip", "shasum": "", "reference": "de036ec91d55d2a9e0db2ba975b512cdb1c23921"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/3.0.2"}, "time": "2022-02-10T06:55:38+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "474fb9edb7ab891665d3bfc6317f42a0a150454b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/474fb9edb7ab891665d3bfc6317f42a0a150454b", "type": "zip", "shasum": "", "reference": "474fb9edb7ab891665d3bfc6317f42a0a150454b"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/3.0.1"}, "time": "2020-11-30T07:43:24+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "edf8a461cf1d4005f19fb0b6b8b95a9f7fa0adc4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/edf8a461cf1d4005f19fb0b6b8b95a9f7fa0adc4", "type": "zip", "shasum": "", "reference": "edf8a461cf1d4005f19fb0b6b8b95a9f7fa0adc4"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/global-state/tree/master"}, "time": "2019-02-01T05:30:01+00:00", "require": {"php": "^7.2", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "funding": "__unset"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "type": "zip", "shasum": "", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/2.0.0"}, "time": "2017-04-27T15:39:26+00:00", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bc37d50fea7d017d3d340f230811c9f1d7280af4", "type": "zip", "shasum": "", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/1.1.1"}, "time": "2015-10-12T03:26:01+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "23af31f402993cfd94e99cbc4b782e9a78eb0e97"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/23af31f402993cfd94e99cbc4b782e9a78eb0e97", "type": "zip", "shasum": "", "reference": "23af31f402993cfd94e99cbc4b782e9a78eb0e97"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/global-state/tree/master"}, "time": "2015-06-21T15:11:22+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/global-state.git", "type": "git", "reference": "c7428acdb62ece0a45e6306f1ae85e1c05b09c01"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/c7428acdb62ece0a45e6306f1ae85e1c05b09c01", "type": "zip", "shasum": "", "reference": "c7428acdb62ece0a45e6306f1ae85e1c05b09c01"}, "time": "2014-10-06T09:23:50+00:00"}]}, "security-advisories": [], "last-modified": "Fri, 29 Aug 2025 11:29:44 GMT"}