{"minified": "composer/2.0", "packages": {"phar-io/version": [{"name": "phar-io/version", "description": "Library for handling version information and constraints", "keywords": [], "homepage": "", "version": "3.2.1", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "source": {"url": "https://github.com/phar-io/version.git", "type": "git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "type": "zip", "shasum": "", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "type": "library", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "funding": [], "time": "2022-02-21T01:04:05+00:00", "autoload": {"classmap": ["src/"]}, "require": {"php": "^7.2 || ^8.0"}}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/version.git", "type": "git", "reference": "c494dccd6802fd111a62ea5a474b33c0fd3dc76b"}, "dist": {"url": "https://api.github.com/repos/phar-io/version/zipball/c494dccd6802fd111a62ea5a474b33c0fd3dc76b", "type": "zip", "shasum": "", "reference": "c494dccd6802fd111a62ea5a474b33c0fd3dc76b"}, "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.0"}, "time": "2022-02-20T23:23:05+00:00"}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/version.git", "type": "git", "reference": "15a90844ad40f127afd244c0cad228de2a80052a"}, "dist": {"url": "https://api.github.com/repos/phar-io/version/zipball/15a90844ad40f127afd244c0cad228de2a80052a", "type": "zip", "shasum": "", "reference": "15a90844ad40f127afd244c0cad228de2a80052a"}, "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.1.1"}, "time": "2022-02-07T21:56:48+00:00"}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/version.git", "type": "git", "reference": "bae7c545bef187884426f042434e561ab1ddb182"}, "dist": {"url": "https://api.github.com/repos/phar-io/version/zipball/bae7c545bef187884426f042434e561ab1ddb182", "type": "zip", "shasum": "", "reference": "bae7c545bef187884426f042434e561ab1ddb182"}, "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.1.0"}, "time": "2021-02-23T14:00:09+00:00"}, {"version": "3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/version.git", "type": "git", "reference": "e4782611070e50613683d2b9a57730e9a3ba5451"}, "dist": {"url": "https://api.github.com/repos/phar-io/version/zipball/e4782611070e50613683d2b9a57730e9a3ba5451", "type": "zip", "shasum": "", "reference": "e4782611070e50613683d2b9a57730e9a3ba5451"}, "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.0.4"}, "time": "2020-12-13T23:18:30+00:00"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/version.git", "type": "git", "reference": "726c026815142e4f8677b7cb7f2249c9ffb7ecae"}, "dist": {"url": "https://api.github.com/repos/phar-io/version/zipball/726c026815142e4f8677b7cb7f2249c9ffb7ecae", "type": "zip", "shasum": "", "reference": "726c026815142e4f8677b7cb7f2249c9ffb7ecae"}, "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.0.3"}, "time": "2020-11-30T09:21:21+00:00"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/version.git", "type": "git", "reference": "c6bb6825def89e0a32220f88337f8ceaf1975fa0"}, "dist": {"url": "https://api.github.com/repos/phar-io/version/zipball/c6bb6825def89e0a32220f88337f8ceaf1975fa0", "type": "zip", "shasum": "", "reference": "c6bb6825def89e0a32220f88337f8ceaf1975fa0"}, "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/master"}, "time": "2020-06-27T14:39:04+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/version.git", "type": "git", "reference": "d06a5000ac1a258a7d035295f0bd4ae7c859bc4f"}, "dist": {"url": "https://api.github.com/repos/phar-io/version/zipball/d06a5000ac1a258a7d035295f0bd4ae7c859bc4f", "type": "zip", "shasum": "", "reference": "d06a5000ac1a258a7d035295f0bd4ae7c859bc4f"}, "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.0.1"}, "time": "2020-05-09T21:27:55+00:00", "require": {"php": "^7.2"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/version.git", "type": "git", "reference": "1f4eccfe4d486824b9f2b5f21f3442ee7f529351"}, "dist": {"url": "https://api.github.com/repos/phar-io/version/zipball/1f4eccfe4d486824b9f2b5f21f3442ee7f529351", "type": "zip", "shasum": "", "reference": "1f4eccfe4d486824b9f2b5f21f3442ee7f529351"}, "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/master"}, "time": "2020-05-05T22:00:38+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/version.git", "type": "git", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6"}, "dist": {"url": "https://api.github.com/repos/phar-io/version/zipball/45a2ec53a73c70ce41d55cedef9063630abaf1b6", "type": "zip", "shasum": "", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6"}, "time": "2018-07-08T19:19:57+00:00", "require": {"php": "^5.6 || ^7.0"}, "funding": "__unset"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/version.git", "type": "git", "reference": "872340c7caa93bc4a2ce20d267498f8e578c856b"}, "dist": {"url": "https://api.github.com/repos/phar-io/version/zipball/872340c7caa93bc4a2ce20d267498f8e578c856b", "type": "zip", "shasum": "", "reference": "872340c7caa93bc4a2ce20d267498f8e578c856b"}, "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/2.0.0"}, "time": "2018-06-23T10:30:07+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/version.git", "type": "git", "reference": "a70c0ced4be299a63d32fa96d9281d03e94041df"}, "dist": {"url": "https://api.github.com/repos/phar-io/version/zipball/a70c0ced4be299a63d32fa96d9281d03e94041df", "type": "zip", "shasum": "", "reference": "a70c0ced4be299a63d32fa96d9281d03e94041df"}, "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/master"}, "time": "2017-03-05T17:38:23+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/version.git", "type": "git", "reference": "791e49e8f112b7b917cbc78c28a577870f2302ee"}, "dist": {"url": "https://api.github.com/repos/phar-io/version/zipball/791e49e8f112b7b917cbc78c28a577870f2302ee", "type": "zip", "shasum": "", "reference": "791e49e8f112b7b917cbc78c28a577870f2302ee"}, "time": "2017-03-03T17:14:15+00:00"}]}, "security-advisories": [], "last-modified": "Sat, 30 Mar 2024 00:18:48 GMT"}