{"minified": "composer/2.0", "packages": {"doctrine/instantiator": [{"name": "doctrine/instantiator", "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "keywords": ["constructor", "instantiate"], "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "version": "2.0.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "type": "zip", "shasum": "", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0"}, "type": "library", "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:23:10+00:00", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "require": {"php": "^8.1"}, "require-dev": {"ext-phar": "*", "ext-pdo": "*", "doctrine/coding-standard": "^11", "phpbench/phpbench": "^1.2", "phpstan/phpstan": "^1.9.4", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5.27", "vimeo/psalm": "^5.4"}}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/0a0fa9780f5d4e507415a065172d26a98d02047b", "type": "zip", "shasum": "", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.5.0"}, "time": "2022-12-30T00:15:36+00:00", "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"ext-phar": "*", "ext-pdo": "*", "doctrine/coding-standard": "^9 || ^11", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}}, {"version": "1.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/10dcfce151b967d20fde1b34ae6640712c3891bc", "type": "zip", "shasum": "", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc"}, "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.4.1"}, "time": "2022-03-03T08:28:38+00:00", "require-dev": {"ext-phar": "*", "ext-pdo": "*", "doctrine/coding-standard": "^9", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.22"}}, {"version": "1.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/d56bf6102915de5702778fe20f2de3b2fe570b5b", "type": "zip", "shasum": "", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b"}, "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.4.0"}, "time": "2020-11-10T18:47:58+00:00", "require-dev": {"ext-phar": "*", "ext-pdo": "*", "doctrine/coding-standard": "^8.0", "phpbench/phpbench": "^0.13 || 1.0.0-alpha2", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}}, {"version": "1.3.1", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "f350df0268e904597e3bd9c4685c53e0e333feea"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/f350df0268e904597e3bd9c4685c53e0e333feea", "type": "zip", "shasum": "", "reference": "f350df0268e904597e3bd9c4685c53e0e333feea"}, "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.3.1"}, "time": "2020-05-29T17:27:14+00:00", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "require-dev": {"ext-phar": "*", "ext-pdo": "*", "doctrine/coding-standard": "^6.0", "phpbench/phpbench": "^0.13", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-shim": "^0.11", "phpunit/phpunit": "^7.0"}}, {"version": "1.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "ae466f726242e637cebdd526a7d991b9433bacf1"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/ae466f726242e637cebdd526a7d991b9433bacf1", "type": "zip", "shasum": "", "reference": "ae466f726242e637cebdd526a7d991b9433bacf1"}, "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.3.0"}, "time": "2019-10-21T16:45:58+00:00", "require": {"php": "^7.1"}}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "a2c590166b2133a4633738648b6b064edae0814a"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/a2c590166b2133a4633738648b6b064edae0814a", "type": "zip", "shasum": "", "reference": "a2c590166b2133a4633738648b6b064edae0814a"}, "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.2.0"}, "time": "2019-03-17T17:37:11+00:00"}, {"homepage": "https://github.com/doctrine/instantiator", "version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "185b8868aa9bf7159f5f953ed5afb2d7fcdc3bda"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/185b8868aa9bf7159f5f953ed5afb2d7fcdc3bda", "type": "zip", "shasum": "", "reference": "185b8868aa9bf7159f5f953ed5afb2d7fcdc3bda"}, "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.1.0"}, "time": "2017-07-22T11:58:36+00:00", "require-dev": {"ext-phar": "*", "ext-pdo": "*", "phpunit/phpunit": "^6.2.3", "squizlabs/php_codesniffer": "^3.0.2", "athletic/athletic": "~0.1.8"}}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/8e884e78f9f0eb1329e445619e04456e64d8051d", "type": "zip", "shasum": "", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d"}, "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.0.5"}, "time": "2015-06-14T21:17:01+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require": {"php": ">=5.3,<8.0-DEV"}, "require-dev": {"ext-phar": "*", "ext-pdo": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0", "athletic/athletic": "~0.1.8"}}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "f976e5de371104877ebc89bd8fecb0019ed9c119"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/f976e5de371104877ebc89bd8fecb0019ed9c119", "type": "zip", "shasum": "", "reference": "f976e5de371104877ebc89bd8fecb0019ed9c119"}, "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.0.4"}, "time": "2014-10-13T12:58:55+00:00", "autoload": {"psr-0": {"Doctrine\\Instantiator\\": "src"}}, "require-dev": {"ext-phar": "*", "ext-pdo": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "2.0.*@ALPHA", "athletic/athletic": "~0.1.8"}}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "8806c41c178ad4a2e87294b851d730779555d252"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/8806c41c178ad4a2e87294b851d730779555d252", "type": "zip", "shasum": "", "reference": "8806c41c178ad4a2e87294b851d730779555d252"}, "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.0.3"}, "time": "2014-10-04T22:48:25+00:00", "require": {"php": "~5.3"}}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "26404e0c90565b614ee76b988b9bc8790d77f590"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/26404e0c90565b614ee76b988b9bc8790d77f590", "type": "zip", "shasum": "", "reference": "26404e0c90565b614ee76b988b9bc8790d77f590"}, "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.0.2"}, "time": "2014-08-25T15:09:25+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "bbb57e04abb15f4a4eb3778158f01280bbb982cd"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/bbb57e04abb15f4a4eb3778158f01280bbb982cd", "type": "zip", "shasum": "", "reference": "bbb57e04abb15f4a4eb3778158f01280bbb982cd"}, "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.0.1"}, "time": "2014-08-14T14:51:48+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/doctrine/instantiator.git", "type": "git", "reference": "7547e2cc5d576f6997921b628eee421e739df806"}, "dist": {"url": "https://api.github.com/repos/doctrine/instantiator/zipball/7547e2cc5d576f6997921b628eee421e739df806", "type": "zip", "shasum": "", "reference": "7547e2cc5d576f6997921b628eee421e739df806"}, "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.0.0"}, "time": "2014-08-13T11:49:22+00:00"}]}, "security-advisories": [], "last-modified": "Tu<PERSON>, 09 Apr 2024 15:21:47 GMT"}