{"minified": "composer/2.0", "packages": {"phpdocumentor/reflection-common": [{"name": "phpdocumentor/reflection-common", "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "keywords": ["phpdoc", "reflection", "static analysis", "phpDocumentor", "FQSEN"], "homepage": "http://www.phpdoc.org", "version": "2.2.0", "version_normalized": "*******", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/phpDocumentor/ReflectionCommon.git", "type": "git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "type": "zip", "shasum": "", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "type": "library", "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "funding": [], "time": "2020-06-27T09:03:43+00:00", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "require": {"php": "^7.2 || ^8.0"}}, {"version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionCommon.git", "type": "git", "reference": "6568f4687e5b41b054365f9ae03fcb1ed5f2069b"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/6568f4687e5b41b054365f9ae03fcb1ed5f2069b", "type": "zip", "shasum": "", "reference": "6568f4687e5b41b054365f9ae03fcb1ed5f2069b"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/master"}, "time": "2020-04-27T09:25:28+00:00", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "require": {"php": ">=7.1"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionCommon.git", "type": "git", "reference": "63a995caa1ca9e5590304cd845c15ad6d482a62a"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/63a995caa1ca9e5590304cd845c15ad6d482a62a", "type": "zip", "shasum": "", "reference": "63a995caa1ca9e5590304cd845c15ad6d482a62a"}, "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.0.0"}, "time": "2018-08-07T13:53:10+00:00", "require-dev": {"phpunit/phpunit": "~6"}, "funding": "__unset"}, {"version": "2.0.0-beta1", "version_normalized": "*******-beta1", "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/master"}}, {"version": "2.0.0-alpha3", "version_normalized": "*******-alpha3", "source": {"url": "https://github.com/phpDocumentor/ReflectionCommon.git", "type": "git", "reference": "eedd98e8bc9cfd924f056b4b5847066d4a250d2f"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/eedd98e8bc9cfd924f056b4b5847066d4a250d2f", "type": "zip", "shasum": "", "reference": "eedd98e8bc9cfd924f056b4b5847066d4a250d2f"}, "time": "2018-06-13T21:44:20+00:00", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src"]}}, "require-dev": {"phpunit/phpunit": "^6"}}, {"version": "2.0.0-alpha2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/phpDocumentor/ReflectionCommon.git", "type": "git", "reference": "b775523cbbcbbcdd54f6a9dc0902ebe8c76ff511"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/b775523cbbcbbcdd54f6a9dc0902ebe8c76ff511", "type": "zip", "shasum": "", "reference": "b775523cbbcbbcdd54f6a9dc0902ebe8c76ff511"}, "time": "2018-02-14T18:51:33+00:00"}, {"version": "2.0.0-alpha1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/phpDocumentor/ReflectionCommon.git", "type": "git", "reference": "e8e68dc40f300e50f5a3b254bd7b41eecdc4ddca"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/e8e68dc40f300e50f5a3b254bd7b41eecdc4ddca", "type": "zip", "shasum": "", "reference": "e8e68dc40f300e50f5a3b254bd7b41eecdc4ddca"}, "time": "2018-01-18T22:08:35+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require-dev": {"phpunit/phpunit": "^6.5"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionCommon.git", "type": "git", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6", "type": "zip", "shasum": "", "reference": "21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6"}, "time": "2017-09-11T18:02:19+00:00", "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.6"}}, {"version": "1.0", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionCommon.git", "type": "git", "reference": "144c307535e82c8fdcaacbcfc1d6d8eeb896687c"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/144c307535e82c8fdcaacbcfc1d6d8eeb896687c", "type": "zip", "shasum": "", "reference": "144c307535e82c8fdcaacbcfc1d6d8eeb896687c"}, "time": "2015-12-27T11:43:31+00:00"}, {"version": "0.3", "version_normalized": "*******"}, {"version": "0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionCommon.git", "type": "git", "reference": "8dfc66d8a72f6741ed9f5a39227f99f78ae8d9a5"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/8dfc66d8a72f6741ed9f5a39227f99f78ae8d9a5", "type": "zip", "shasum": "", "reference": "8dfc66d8a72f6741ed9f5a39227f99f78ae8d9a5"}, "time": "2015-08-09T12:13:56+00:00"}, {"version": "0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phpDocumentor/ReflectionCommon.git", "type": "git", "reference": "f10c8a4e2afd1b106550b0a5b0e8db81742377f1"}, "dist": {"url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/f10c8a4e2afd1b106550b0a5b0e8db81742377f1", "type": "zip", "shasum": "", "reference": "f10c8a4e2afd1b106550b0a5b0e8db81742377f1"}, "time": "2015-06-12T17:27:38+00:00", "require": "__unset"}]}, "security-advisories": [], "last-modified": "Fri, 29 Mar 2024 13:11:58 GMT"}