{"minified": "composer/2.0", "packages": {"sebastian/version": [{"name": "sebastian/version", "description": "Library that helps with managing the version number of Git-hosted PHP projects", "keywords": [], "homepage": "https://github.com/sebastian<PERSON>mann/version", "version": "6.0.0", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "3e6ccf7657d4f0a59200564b08cead899313b53c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/3e6ccf7657d4f0a59200564b08cead899313b53c", "type": "zip", "shasum": "", "reference": "3e6ccf7657d4f0a59200564b08cead899313b53c"}, "type": "library", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/version/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T05:00:38+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "require": {"php": ">=8.3"}}, {"version": "5.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "c687e3387b99f5b03b6caa64c74b63e2936ff874"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c687e3387b99f5b03b6caa64c74b63e2936ff874", "type": "zip", "shasum": "", "reference": "c687e3387b99f5b03b6caa64c74b63e2936ff874"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/version/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/5.0.2"}, "time": "2024-10-09T05:16:32+00:00", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "require": {"php": ">=8.2"}}, {"version": "5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "45c9debb7d039ce9b97de2f749c2cf5832a06ac4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/45c9debb7d039ce9b97de2f749c2cf5832a06ac4", "type": "zip", "shasum": "", "reference": "45c9debb7d039ce9b97de2f749c2cf5832a06ac4"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/version/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/5.0.1"}, "time": "2024-07-03T05:13:08+00:00"}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "13999475d2cb1ab33cb73403ba356a814fdbb001"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/13999475d2cb1ab33cb73403ba356a814fdbb001", "type": "zip", "shasum": "", "reference": "13999475d2cb1ab33cb73403ba356a814fdbb001"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/version/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/5.0.0"}, "time": "2024-02-02T06:10:47+00:00"}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c51fa83a5d8f43f1402e3f32a005e6262244ef17", "type": "zip", "shasum": "", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/4.0.1"}, "time": "2023-02-07T11:34:05+00:00", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "require": {"php": ">=8.1"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "5facf5a20311ac44f79221274cdeb6c569ca11dd"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/5facf5a20311ac44f79221274cdeb6c569ca11dd", "type": "zip", "shasum": "", "reference": "5facf5a20311ac44f79221274cdeb6c569ca11dd"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/4.0.0"}, "time": "2023-02-03T07:11:37+00:00"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "c6c1022351a901512170118436c764e473f6de8c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c6c1022351a901512170118436c764e473f6de8c", "type": "zip", "shasum": "", "reference": "c6c1022351a901512170118436c764e473f6de8c"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/3.0.2"}, "time": "2020-09-28T06:39:44+00:00", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "require": {"php": ">=7.3"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "626586115d0ed31cb71483be55beb759b5af5a3c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/626586115d0ed31cb71483be55beb759b5af5a3c", "type": "zip", "shasum": "", "reference": "626586115d0ed31cb71483be55beb759b5af5a3c"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/3.0.1"}, "time": "2020-06-26T12:18:43+00:00", "require": {"php": "^7.3 || ^8.0"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "0411bde656dce64202b39c2f4473993a9081d39e"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/0411bde656dce64202b39c2f4473993a9081d39e", "type": "zip", "shasum": "", "reference": "0411bde656dce64202b39c2f4473993a9081d39e"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/master"}, "time": "2020-01-21T06:36:37+00:00", "require": {"php": "^7.3"}, "funding": "__unset"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "type": "zip", "shasum": "", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "time": "2016-10-03T07:35:21+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require": {"php": ">=5.6"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "c829badbd8fdf16a0bad8aa7fa7971c029f1b9c5"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/version/zipball/c829badbd8fdf16a0bad8aa7fa7971c029f1b9c5", "type": "zip", "shasum": "", "reference": "c829badbd8fdf16a0bad8aa7fa7971c029f1b9c5"}, "time": "2016-02-04T12:56:52+00:00"}, {"version": "1.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "58b3a85e7999757d6ad81c787a1fbf5ff6c628c6"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/58b3a85e7999757d6ad81c787a1fbf5ff6c628c6", "type": "zip", "shasum": "", "reference": "58b3a85e7999757d6ad81c787a1fbf5ff6c628c6"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/1.0.6"}, "time": "2015-06-21T13:59:46+00:00", "extra": "__unset", "require": "__unset"}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "ab931d46cd0d3204a91e1b9a40c4bc13032b58e4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/ab931d46cd0d3204a91e1b9a40c4bc13032b58e4", "type": "zip", "shasum": "", "reference": "ab931d46cd0d3204a91e1b9a40c4bc13032b58e4"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/master"}, "time": "2015-02-24T06:35:25+00:00"}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "a77d9123f8e809db3fbdea15038c27a95da4058b"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/a77d9123f8e809db3fbdea15038c27a95da4058b", "type": "zip", "shasum": "", "reference": "a77d9123f8e809db3fbdea15038c27a95da4058b"}, "time": "2014-12-15T14:25:24+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "b6e1f0cf6b9e1ec409a0d3e2f2a5fb0998e36b43"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/b6e1f0cf6b9e1ec409a0d3e2f2a5fb0998e36b43", "type": "zip", "shasum": "", "reference": "b6e1f0cf6b9e1ec409a0d3e2f2a5fb0998e36b43"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/1.0.3"}, "time": "2014-03-07T15:35:33+00:00"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "b5d32aad75602e50b6b588583bd62f338f0f7a50"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/b5d32aad75602e50b6b588583bd62f338f0f7a50", "type": "zip", "shasum": "", "reference": "b5d32aad75602e50b6b588583bd62f338f0f7a50"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/1.0.2"}, "time": "2014-02-12T16:30:13+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "5d879c9144bf41d5a06132f3d194c01ef6c6b3f4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/5d879c9144bf41d5a06132f3d194c01ef6c6b3f4", "type": "zip", "shasum": "", "reference": "5d879c9144bf41d5a06132f3d194c01ef6c6b3f4"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/master"}, "time": "2013-05-29T10:02:00+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/version.git", "type": "git", "reference": "16b021aed448b654ae05846e394e057e9a6f04cb"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/16b021aed448b654ae05846e394e057e9a6f04cb", "type": "zip", "shasum": "", "reference": "16b021aed448b654ae05846e394e057e9a6f04cb"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/1.0.0"}, "time": "2013-01-05T14:27:32+00:00"}]}, "security-advisories": [], "last-modified": "Fri, 07 Feb 2025 05:00:54 GMT"}