{"minified": "composer/2.0", "packages": {"phar-io/manifest": [{"name": "phar-io/manifest", "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "keywords": [], "homepage": "", "version": "2.0.4", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "source": {"url": "https://github.com/phar-io/manifest.git", "type": "git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "type": "zip", "shasum": "", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "type": "library", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require": {"php": "^7.2 || ^8.0", "ext-dom": "*", "ext-phar": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1"}}, {"version": "2.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/manifest.git", "type": "git", "reference": "97803eca37d319dfa7826cc2437fc020857acb53"}, "dist": {"url": "https://api.github.com/repos/phar-io/manifest/zipball/97803eca37d319dfa7826cc2437fc020857acb53", "type": "zip", "shasum": "", "reference": "97803eca37d319dfa7826cc2437fc020857acb53"}, "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.3"}, "funding": [], "time": "2021-07-20T11:28:43+00:00", "require": {"php": "^7.2 || ^8.0", "ext-dom": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1"}}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/manifest.git", "type": "git", "reference": "581196202f9418ec46da57427e05656b0cfe8702"}, "dist": {"url": "https://api.github.com/repos/phar-io/manifest/zipball/581196202f9418ec46da57427e05656b0cfe8702", "type": "zip", "shasum": "", "reference": "581196202f9418ec46da57427e05656b0cfe8702"}, "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.2"}, "time": "2021-07-20T11:12:10+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/manifest.git", "type": "git", "reference": "85265efd3af7ba3ca4b2a2c34dbfc5788dd29133"}, "dist": {"url": "https://api.github.com/repos/phar-io/manifest/zipball/85265efd3af7ba3ca4b2a2c34dbfc5788dd29133", "type": "zip", "shasum": "", "reference": "85265efd3af7ba3ca4b2a2c34dbfc5788dd29133"}, "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/master"}, "time": "2020-06-27T14:33:11+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/manifest.git", "type": "git", "reference": "549a9ec52cf97f20d375fca417a9eefd25f05fd5"}, "dist": {"url": "https://api.github.com/repos/phar-io/manifest/zipball/549a9ec52cf97f20d375fca417a9eefd25f05fd5", "type": "zip", "shasum": "", "reference": "549a9ec52cf97f20d375fca417a9eefd25f05fd5"}, "time": "2020-05-09T22:05:33+00:00", "require": {"php": "^7.2", "ext-dom": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1"}}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/manifest.git", "type": "git", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4"}, "dist": {"url": "https://api.github.com/repos/phar-io/manifest/zipball/7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "type": "zip", "shasum": "", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4"}, "time": "2018-07-08T19:23:20+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require": {"php": "^5.6 || ^7.0", "ext-dom": "*", "ext-phar": "*", "phar-io/version": "^2.0"}, "funding": "__unset"}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/manifest.git", "type": "git", "reference": "29654cc208939d684071d5725dd1ad1f3e5d1733"}, "dist": {"url": "https://api.github.com/repos/phar-io/manifest/zipball/29654cc208939d684071d5725dd1ad1f3e5d1733", "type": "zip", "shasum": "", "reference": "29654cc208939d684071d5725dd1ad1f3e5d1733"}, "time": "2018-06-23T10:38:44+00:00", "require": {"php": "^5.6 || ^7.0", "ext-dom": "*", "ext-phar": "*", "phar-io/version": "2.0.0"}}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/manifest.git", "type": "git", "reference": "2df402786ab5368a0169091f61a7c1e0eb6852d0"}, "dist": {"url": "https://api.github.com/repos/phar-io/manifest/zipball/2df402786ab5368a0169091f61a7c1e0eb6852d0", "type": "zip", "shasum": "", "reference": "2df402786ab5368a0169091f61a7c1e0eb6852d0"}, "time": "2017-03-05T18:14:27+00:00", "require": {"php": "^5.6 || ^7.0", "ext-dom": "*", "ext-phar": "*", "phar-io/version": "^1.0.1"}}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/phar-io/manifest.git", "type": "git", "reference": "965eca4cabb5edc2fb315ce911994a13cb1dc2d7"}, "dist": {"url": "https://api.github.com/repos/phar-io/manifest/zipball/965eca4cabb5edc2fb315ce911994a13cb1dc2d7", "type": "zip", "shasum": "", "reference": "965eca4cabb5edc2fb315ce911994a13cb1dc2d7"}, "time": "2017-03-05T17:52:40+00:00"}]}, "security-advisories": [], "last-modified": "Wed, 03 Apr 2024 12:48:20 GMT"}