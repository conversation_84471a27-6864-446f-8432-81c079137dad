{"minified": "composer/2.0", "packages": {"symfony/polyfill-ctype": [{"name": "symfony/polyfill-ctype", "description": "Symfony polyfill for ctype functions", "keywords": ["compatibility", "portable", "polyfill", "ctype"], "homepage": "https://symfony.com", "version": "v1.33.0", "version_normalized": "********", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "type": "zip", "shasum": "", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "type": "library", "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "require": {"php": ">=7.2"}, "suggest": {"ext-ctype": "For best performance"}, "provide": {"ext-ctype": "*"}}, {"version": "v1.32.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}]}, {"version": "v1.31.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}}, {"version": "v1.30.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "0424dff1c58f028c451efff2045f5d92410bd540"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/0424dff1c58f028c451efff2045f5d92410bd540", "type": "zip", "shasum": "", "reference": "0424dff1c58f028c451efff2045f5d92410bd540"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.30.0"}, "time": "2024-05-31T15:07:36+00:00", "require": {"php": ">=7.1"}}, {"version": "v1.29.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/ef4d7e442ca910c4764bce785146269b30cb5fc4", "type": "zip", "shasum": "", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.29.0"}, "time": "2024-01-29T20:11:03+00:00"}, {"version": "v1.28.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb", "type": "zip", "shasum": "", "reference": "ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.28.0"}, "time": "2023-01-26T09:26:14+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.28-dev"}}}, {"version": "v1.27.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "5bbc823adecdae860bb64756d639ecfec17b050a"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/5bbc823adecdae860bb64756d639ecfec17b050a", "type": "zip", "shasum": "", "reference": "5bbc823adecdae860bb64756d639ecfec17b050a"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.27.0"}, "time": "2022-11-03T14:55:06+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.27-dev"}}}, {"version": "v1.26.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4", "type": "zip", "shasum": "", "reference": "6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.26-dev"}}}, {"version": "v1.25.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "30885182c981ab175d4d034db0f6f469898070ab"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/30885182c981ab175d4d034db0f6f469898070ab", "type": "zip", "shasum": "", "reference": "30885182c981ab175d4d034db0f6f469898070ab"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.25.0"}, "time": "2021-10-20T20:35:02+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.23-dev"}}}, {"version": "v1.24.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.24.0"}}, {"version": "v1.23.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "46cd95797e9df938fdd2b03693b5fca5e64b01ce"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/46cd95797e9df938fdd2b03693b5fca5e64b01ce", "type": "zip", "shasum": "", "reference": "46cd95797e9df938fdd2b03693b5fca5e64b01ce"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.23.0"}, "time": "2021-02-19T12:13:01+00:00", "provide": "__unset"}, {"version": "v1.22.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "c6c942b1ac76c82448322025e084cadc56048b4e"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/c6c942b1ac76c82448322025e084cadc56048b4e", "type": "zip", "shasum": "", "reference": "c6c942b1ac76c82448322025e084cadc56048b4e"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.22.1"}, "time": "2021-01-07T16:49:33+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.22-dev"}}}, {"version": "v1.22.0", "version_normalized": "********", "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.22.0"}}, {"version": "v1.20.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "f4ba089a5b6366e453971d3aad5fe8e897b37f41"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/f4ba089a5b6366e453971d3aad5fe8e897b37f41", "type": "zip", "shasum": "", "reference": "f4ba089a5b6366e453971d3aad5fe8e897b37f41"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.20.0"}, "time": "2020-10-23T14:02:19+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.20-dev"}}}, {"version": "v1.19.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "aed596913b70fae57be53d86faa2e9ef85a2297b"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/aed596913b70fae57be53d86faa2e9ef85a2297b", "type": "zip", "shasum": "", "reference": "aed596913b70fae57be53d86faa2e9ef85a2297b"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.19.0"}, "time": "2020-10-23T09:01:57+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-main": "1.19-dev"}}, "require": {"php": ">=5.3.3"}}, {"version": "v1.18.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "1c302646f6efc070cd46856e600e5e0684d6b454"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/1c302646f6efc070cd46856e600e5e0684d6b454", "type": "zip", "shasum": "", "reference": "1c302646f6efc070cd46856e600e5e0684d6b454"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.18.0"}, "time": "2020-07-14T12:35:20+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.18-dev"}}}, {"version": "v1.18.0", "version_normalized": "********"}, {"version": "v1.17.1", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "2edd75b8b35d62fd3eeabba73b26b8f1f60ce13d"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/2edd75b8b35d62fd3eeabba73b26b8f1f60ce13d", "type": "zip", "shasum": "", "reference": "2edd75b8b35d62fd3eeabba73b26b8f1f60ce13d"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.17.1"}, "time": "2020-06-06T08:46:27+00:00", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}, "branch-alias": {"dev-master": "1.17-dev"}}}, {"version": "v1.17.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "e94c8b1bbe2bc77507a1056cdb06451c75b427f9"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/e94c8b1bbe2bc77507a1056cdb06451c75b427f9", "type": "zip", "shasum": "", "reference": "e94c8b1bbe2bc77507a1056cdb06451c75b427f9"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.17.0"}, "time": "2020-05-12T16:14:59+00:00", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}}, {"version": "v1.16.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "1aab00e39cebaef4d8652497f46c15c1b7e45294"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/1aab00e39cebaef4d8652497f46c15c1b7e45294", "type": "zip", "shasum": "", "reference": "1aab00e39cebaef4d8652497f46c15c1b7e45294"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.16.0"}, "time": "2020-05-08T16:50:20+00:00", "extra": {"branch-alias": {"dev-master": "1.16-dev"}}}, {"version": "v1.15.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "4719fa9c18b0464d399f1a63bf624b42b6fa8d14"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/4719fa9c18b0464d399f1a63bf624b42b6fa8d14", "type": "zip", "shasum": "", "reference": "4719fa9c18b0464d399f1a63bf624b42b6fa8d14"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.15.0"}, "time": "2020-02-27T09:26:54+00:00", "extra": {"branch-alias": {"dev-master": "1.15-dev"}}}, {"version": "v1.14.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "fbdeaec0df06cf3d51c93de80c7eb76e271f5a38"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/fbdeaec0df06cf3d51c93de80c7eb76e271f5a38", "type": "zip", "shasum": "", "reference": "fbdeaec0df06cf3d51c93de80c7eb76e271f5a38"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/master"}, "time": "2020-01-13T11:15:53+00:00", "extra": {"branch-alias": {"dev-master": "1.14-dev"}}, "funding": "__unset"}, {"version": "v1.13.2", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "f8f0b461be3385e56d6de3dbb5a0df24c0c275e3"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/f8f0b461be3385e56d6de3dbb5a0df24c0c275e3", "type": "zip", "shasum": "", "reference": "f8f0b461be3385e56d6de3dbb5a0df24c0c275e3"}, "time": "2019-11-27T13:56:44+00:00", "extra": {"branch-alias": {"dev-master": "1.13-dev"}}}, {"version": "v1.13.1", "version_normalized": "********"}, {"version": "v1.13.0", "version_normalized": "********"}, {"version": "v1.12.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "550ebaac289296ce228a706d0867afc34687e3f4"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/550ebaac289296ce228a706d0867afc34687e3f4", "type": "zip", "shasum": "", "reference": "550ebaac289296ce228a706d0867afc34687e3f4"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.12.0"}, "time": "2019-08-06T08:03:45+00:00", "extra": {"branch-alias": {"dev-master": "1.12-dev"}}}, {"version": "v1.11.0", "version_normalized": "********", "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "82ebae02209c21113908c229e9883c419720738a"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/82ebae02209c21113908c229e9883c419720738a", "type": "zip", "shasum": "", "reference": "82ebae02209c21113908c229e9883c419720738a"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/master"}, "time": "2019-02-06T07:57:58+00:00", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}}, {"version": "v1.10.0", "version_normalized": "********", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "e3d826245268269cd66f8326bd8bc066687b4a19"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/e3d826245268269cd66f8326bd8bc066687b4a19", "type": "zip", "shasum": "", "reference": "e3d826245268269cd66f8326bd8bc066687b4a19"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.9.0"}, "time": "2018-08-06T14:22:27+00:00", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}}, {"version": "v1.9.0", "version_normalized": "*******"}, {"version": "v1.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/symfony/polyfill-ctype.git", "type": "git", "reference": "7cc359f1b7b80fc25ed7796be7d96adc9b354bae"}, "dist": {"url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/7cc359f1b7b80fc25ed7796be7d96adc9b354bae", "type": "zip", "shasum": "", "reference": "7cc359f1b7b80fc25ed7796be7d96adc9b354bae"}, "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/master"}, "time": "2018-04-30T19:57:29+00:00", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "suggest": "__unset"}]}, "security-advisories": [], "last-modified": "<PERSON><PERSON>, 19 Aug 2025 20:34:45 GMT"}