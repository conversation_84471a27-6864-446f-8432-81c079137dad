{"minified": "composer/2.0", "packages": {"squizlabs/php_codesniffer": [{"name": "squizlabs/php_codesniffer", "description": "PHP_CodeSniffer tokenizes PHP files and detects violations of a defined set of coding standards.", "keywords": ["static analysis", "standards", "phpcs"], "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "version": "4.0.0-RC1", "version_normalized": "*******-RC1", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "ed5e2c22f52c03c63f886ec67dbc697efa0c3f63"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/ed5e2c22f52c03c63f886ec67dbc697efa0c3f63", "type": "zip", "shasum": "", "reference": "ed5e2c22f52c03c63f886ec67dbc697efa0c3f63"}, "type": "library", "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/u/gh/phpcsstandards", "type": "thanks_dev"}], "time": "2025-06-17T22:39:38+00:00", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "bin": ["bin/phpcbf", "bin/phpcs"], "require": {"php": ">=7.2.0", "ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*"}, "require-dev": {"phpunit/phpunit": "^8.0 || ^9.3.4 || ^10.5.32 || ^11.3.3"}}, {"version": "4.0.0beta1", "version_normalized": "*******-beta1", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "b37066133dc2d2d0291187f3120035ef33032caf"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/b37066133dc2d2d0291187f3120035ef33032caf", "type": "zip", "shasum": "", "reference": "b37066133dc2d2d0291187f3120035ef33032caf"}, "time": "2025-05-11T04:14:19+00:00"}, {"description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "version": "3.13.4", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "ad545ea9c1b7d270ce0fc9cbfb884161cd706119"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/ad545ea9c1b7d270ce0fc9cbfb884161cd706119", "type": "zip", "shasum": "", "reference": "ad545ea9c1b7d270ce0fc9cbfb884161cd706119"}, "time": "2025-09-05T05:47:09+00:00", "require": {"php": ">=5.4.0", "ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}}, {"version": "3.13.3", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "5be16e10d07a2b0c629a8fc52c653fd57458bdf8"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/5be16e10d07a2b0c629a8fc52c653fd57458bdf8", "type": "zip", "shasum": "", "reference": "5be16e10d07a2b0c629a8fc52c653fd57458bdf8"}, "time": "2025-09-04T19:19:52+00:00"}, {"version": "3.13.2", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "5b5e3821314f947dd040c70f7992a64eac89025c"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/5b5e3821314f947dd040c70f7992a64eac89025c", "type": "zip", "shasum": "", "reference": "5b5e3821314f947dd040c70f7992a64eac89025c"}, "time": "2025-06-17T22:17:01+00:00"}, {"version": "3.13.1", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "1b71b4dd7e7ef651ac749cea67e513c0c832f4bd"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/1b71b4dd7e7ef651ac749cea67e513c0c832f4bd", "type": "zip", "shasum": "", "reference": "1b71b4dd7e7ef651ac749cea67e513c0c832f4bd"}, "time": "2025-06-12T15:04:34+00:00"}, {"version": "3.13.0", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "65ff2489553b83b4597e89c3b8b721487011d186"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/65ff2489553b83b4597e89c3b8b721487011d186", "type": "zip", "shasum": "", "reference": "65ff2489553b83b4597e89c3b8b721487011d186"}, "time": "2025-05-11T03:36:00+00:00"}, {"version": "3.12.2", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "6d4cf6032d4b718f168c90a96e36c7d0eaacb2aa"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/6d4cf6032d4b718f168c90a96e36c7d0eaacb2aa", "type": "zip", "shasum": "", "reference": "6d4cf6032d4b718f168c90a96e36c7d0eaacb2aa"}, "time": "2025-04-13T04:10:18+00:00"}, {"version": "3.12.1", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "ea16a1f3719783345febd3aab41beb55c8c84bfd"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/ea16a1f3719783345febd3aab41beb55c8c84bfd", "type": "zip", "shasum": "", "reference": "ea16a1f3719783345febd3aab41beb55c8c84bfd"}, "time": "2025-04-04T12:57:55+00:00"}, {"version": "3.12.0", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "2d1b63db139c3c6ea0c927698e5160f8b3b8d630"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/2d1b63db139c3c6ea0c927698e5160f8b3b8d630", "type": "zip", "shasum": "", "reference": "2d1b63db139c3c6ea0c927698e5160f8b3b8d630"}, "time": "2025-03-18T05:04:51+00:00"}, {"version": "3.11.3", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "ba05f990e79cbe69b9f35c8c1ac8dca7eecc3a10"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/ba05f990e79cbe69b9f35c8c1ac8dca7eecc3a10", "type": "zip", "shasum": "", "reference": "ba05f990e79cbe69b9f35c8c1ac8dca7eecc3a10"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/phpcsstandards", "type": "thanks_dev"}], "time": "2025-01-23T17:04:15+00:00"}, {"version": "3.11.2", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "1368f4a58c3c52114b86b1abe8f4098869cb0079"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/1368f4a58c3c52114b86b1abe8f4098869cb0079", "type": "zip", "shasum": "", "reference": "1368f4a58c3c52114b86b1abe8f4098869cb0079"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2024-12-11T16:04:26+00:00"}, {"version": "3.11.1", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "19473c30efe4f7b3cd42522d0b2e6e7f243c6f87"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/19473c30efe4f7b3cd42522d0b2e6e7f243c6f87", "type": "zip", "shasum": "", "reference": "19473c30efe4f7b3cd42522d0b2e6e7f243c6f87"}, "time": "2024-11-16T12:02:36+00:00"}, {"version": "3.11.0", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "70c08f8d20c0eb4fe56f26644dd94dae76a7f450"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/70c08f8d20c0eb4fe56f26644dd94dae76a7f450", "type": "zip", "shasum": "", "reference": "70c08f8d20c0eb4fe56f26644dd94dae76a7f450"}, "time": "2024-11-12T09:53:29+00:00"}, {"version": "3.10.3", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "62d32998e820bddc40f99f8251958aed187a5c9c"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/62d32998e820bddc40f99f8251958aed187a5c9c", "type": "zip", "shasum": "", "reference": "62d32998e820bddc40f99f8251958aed187a5c9c"}, "time": "2024-09-18T10:38:58+00:00"}, {"version": "3.10.2", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "86e5f5dd9a840c46810ebe5ff1885581c42a3017"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/86e5f5dd9a840c46810ebe5ff1885581c42a3017", "type": "zip", "shasum": "", "reference": "86e5f5dd9a840c46810ebe5ff1885581c42a3017"}, "time": "2024-07-21T23:26:44+00:00"}, {"version": "3.10.1", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "8f90f7a53ce271935282967f53d0894f8f1ff877"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/8f90f7a53ce271935282967f53d0894f8f1ff877", "type": "zip", "shasum": "", "reference": "8f90f7a53ce271935282967f53d0894f8f1ff877"}, "time": "2024-05-22T21:24:41+00:00"}, {"version": "3.10.0", "version_normalized": "********", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "57e09801c2fbae2d257b8b75bebb3deeb7e9deb2"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/57e09801c2fbae2d257b8b75bebb3deeb7e9deb2", "type": "zip", "shasum": "", "reference": "57e09801c2fbae2d257b8b75bebb3deeb7e9deb2"}, "time": "2024-05-20T08:11:32+00:00"}, {"version": "3.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "aac1f6f347a5c5ac6bc98ad395007df00990f480"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/aac1f6f347a5c5ac6bc98ad395007df00990f480", "type": "zip", "shasum": "", "reference": "aac1f6f347a5c5ac6bc98ad395007df00990f480"}, "time": "2024-04-23T20:25:34+00:00"}, {"version": "3.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "267a4405fff1d9c847134db3a3c92f1ab7f77909"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/267a4405fff1d9c847134db3a3c92f1ab7f77909", "type": "zip", "shasum": "", "reference": "267a4405fff1d9c847134db3a3c92f1ab7f77909"}, "time": "2024-03-31T21:03:09+00:00"}, {"version": "3.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "d63cee4890a8afaf86a22e51ad4d97c91dd4579b"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/d63cee4890a8afaf86a22e51ad4d97c91dd4579b", "type": "zip", "shasum": "", "reference": "d63cee4890a8afaf86a22e51ad4d97c91dd4579b"}, "time": "2024-02-16T15:06:51+00:00"}, {"version": "3.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "14f5fff1e64118595db5408e946f3a22c75807f7"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/14f5fff1e64118595db5408e946f3a22c75807f7", "type": "zip", "shasum": "", "reference": "14f5fff1e64118595db5408e946f3a22c75807f7"}, "time": "2024-01-11T20:47:48+00:00"}, {"version": "3.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "5805f7a4e4958dbb5e944ef1e6edae0a303765e7"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/5805f7a4e4958dbb5e944ef1e6edae0a303765e7", "type": "zip", "shasum": "", "reference": "5805f7a4e4958dbb5e944ef1e6edae0a303765e7"}, "time": "2023-12-08T12:32:31+00:00", "bin": ["bin/phpcs", "bin/phpcbf"], "require": {"php": ">=5.4.0", "ext-tokenizer": "*", "ext-xmlwriter": "*", "ext-simplexml": "*"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}}, {"homepage": "https://github.com/squizlabs/PHP_CodeSniffer", "version": "3.7.2", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "role": "lead"}], "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "ed8e00df0a83aa96acf703f8c2979ff33341f879"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/ed8e00df0a83aa96acf703f8c2979ff33341f879", "type": "zip", "shasum": "", "reference": "ed8e00df0a83aa96acf703f8c2979ff33341f879"}, "support": {"issues": "https://github.com/squizlabs/PHP_CodeSniffer/issues", "source": "https://github.com/squizlabs/PHP_CodeSniffer", "wiki": "https://github.com/squizlabs/PHP_CodeSniffer/wiki"}, "time": "2023-02-22T23:07:41+00:00", "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}}, {"keywords": ["standards", "phpcs"], "version": "3.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "1359e176e9307e906dc3d890bcc9603ff6d90619"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/1359e176e9307e906dc3d890bcc9603ff6d90619", "type": "zip", "shasum": "", "reference": "1359e176e9307e906dc3d890bcc9603ff6d90619"}, "time": "2022-06-18T07:21:10+00:00"}, {"version": "3.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "a2cd51b45bcaef9c1f2a4bda48f2dd2fa2b95563"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/a2cd51b45bcaef9c1f2a4bda48f2dd2fa2b95563", "type": "zip", "shasum": "", "reference": "a2cd51b45bcaef9c1f2a4bda48f2dd2fa2b95563"}, "time": "2022-06-13T06:31:38+00:00"}, {"version": "3.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "5e4e71592f69da17871dba6e80dd51bce74a351a"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/5e4e71592f69da17871dba6e80dd51bce74a351a", "type": "zip", "shasum": "", "reference": "5e4e71592f69da17871dba6e80dd51bce74a351a"}, "time": "2021-12-12T21:44:58+00:00"}, {"version": "3.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "f268ca40d54617c6e06757f83f699775c9b3ff2e"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/f268ca40d54617c6e06757f83f699775c9b3ff2e", "type": "zip", "shasum": "", "reference": "f268ca40d54617c6e06757f83f699775c9b3ff2e"}, "time": "2021-10-11T04:00:11+00:00"}, {"version": "3.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "ffced0d2c8fa8e6cdc4d695a743271fab6c38625"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/ffced0d2c8fa8e6cdc4d695a743271fab6c38625", "type": "zip", "shasum": "", "reference": "ffced0d2c8fa8e6cdc4d695a743271fab6c38625"}, "time": "2021-04-09T00:54:41+00:00"}, {"version": "3.5.8", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "9d583721a7157ee997f235f327de038e7ea6dac4"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/9d583721a7157ee997f235f327de038e7ea6dac4", "type": "zip", "shasum": "", "reference": "9d583721a7157ee997f235f327de038e7ea6dac4"}, "time": "2020-10-23T02:01:07+00:00"}, {"version": "3.5.7", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "4dbf1d59cd3f155c6f906f018a8574bc53b5be30"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/4dbf1d59cd3f155c6f906f018a8574bc53b5be30", "type": "zip", "shasum": "", "reference": "4dbf1d59cd3f155c6f906f018a8574bc53b5be30"}, "time": "2020-10-22T21:09:11+00:00"}, {"version": "3.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "e97627871a7eab2f70e59166072a6b767d5834e0"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/e97627871a7eab2f70e59166072a6b767d5834e0", "type": "zip", "shasum": "", "reference": "e97627871a7eab2f70e59166072a6b767d5834e0"}, "time": "2020-08-10T04:50:15+00:00"}, {"version": "3.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "73e2e7f57d958e7228fce50dc0c61f58f017f9f6"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/73e2e7f57d958e7228fce50dc0c61f58f017f9f6", "type": "zip", "shasum": "", "reference": "73e2e7f57d958e7228fce50dc0c61f58f017f9f6"}, "time": "2020-04-17T01:09:41+00:00"}, {"version": "3.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "dceec07328401de6211037abbb18bda423677e26"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/dceec07328401de6211037abbb18bda423677e26", "type": "zip", "shasum": "", "reference": "dceec07328401de6211037abbb18bda423677e26"}, "time": "2020-01-30T22:20:29+00:00"}, {"version": "3.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "557a1fc7ac702c66b0bbfe16ab3d55839ef724cb"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/557a1fc7ac702c66b0bbfe16ab3d55839ef724cb", "type": "zip", "shasum": "", "reference": "557a1fc7ac702c66b0bbfe16ab3d55839ef724cb"}, "time": "2019-12-04T04:46:47+00:00"}, {"version": "3.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "65b12cdeaaa6cd276d4c3033a95b9b88b12701e7"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/65b12cdeaaa6cd276d4c3033a95b9b88b12701e7", "type": "zip", "shasum": "", "reference": "65b12cdeaaa6cd276d4c3033a95b9b88b12701e7"}, "time": "2019-10-28T04:36:32+00:00"}, {"version": "3.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "82cd0f854ceca17731d6d019c7098e3755c45060"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/82cd0f854ceca17731d6d019c7098e3755c45060", "type": "zip", "shasum": "", "reference": "82cd0f854ceca17731d6d019c7098e3755c45060"}, "time": "2019-10-16T21:14:26+00:00"}, {"version": "3.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "0afebf16a2e7f1e434920fa976253576151effe9"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/0afebf16a2e7f1e434920fa976253576151effe9", "type": "zip", "shasum": "", "reference": "0afebf16a2e7f1e434920fa976253576151effe9"}, "time": "2019-09-26T23:12:26+00:00"}, {"version": "3.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "b8a7362af1cc1aadb5bd36c3defc4dda2cf5f0a8"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/b8a7362af1cc1aadb5bd36c3defc4dda2cf5f0a8", "type": "zip", "shasum": "", "reference": "b8a7362af1cc1aadb5bd36c3defc4dda2cf5f0a8"}, "time": "2019-04-10T23:49:02+00:00"}, {"version": "3.4.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "5b4333b4010625d29580eb4a41f1e53251be6baa"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/5b4333b4010625d29580eb4a41f1e53251be6baa", "type": "zip", "shasum": "", "reference": "5b4333b4010625d29580eb4a41f1e53251be6baa"}, "time": "2019-03-19T03:22:27+00:00"}, {"homepage": "http://www.squizlabs.com/php-codesniffer", "version": "3.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "379deb987e26c7cd103a7b387aea178baec96e48"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/379deb987e26c7cd103a7b387aea178baec96e48", "type": "zip", "shasum": "", "reference": "379deb987e26c7cd103a7b387aea178baec96e48"}, "time": "2018-12-19T23:57:18+00:00"}, {"version": "3.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "6ad28354c04b364c3c71a34e4a18b629cc3b231e"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/6ad28354c04b364c3c71a34e4a18b629cc3b231e", "type": "zip", "shasum": "", "reference": "6ad28354c04b364c3c71a34e4a18b629cc3b231e"}, "time": "2018-09-23T23:08:17+00:00"}, {"version": "3.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "628a481780561150481a9ec74709092b9759b3ec"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/628a481780561150481a9ec74709092b9759b3ec", "type": "zip", "shasum": "", "reference": "628a481780561150481a9ec74709092b9759b3ec"}, "time": "2018-07-26T23:47:18+00:00"}, {"version": "3.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "d86873af43b4aa9d1f39a3601cc0cfcf02b25266"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/d86873af43b4aa9d1f39a3601cc0cfcf02b25266", "type": "zip", "shasum": "", "reference": "d86873af43b4aa9d1f39a3601cc0cfcf02b25266"}, "time": "2018-06-06T23:58:19+00:00"}, {"version": "3.2.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "4842476c434e375f9d3182ff7b89059583aa8b27"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/4842476c434e375f9d3182ff7b89059583aa8b27", "type": "zip", "shasum": "", "reference": "4842476c434e375f9d3182ff7b89059583aa8b27"}, "time": "2018-02-20T21:35:23+00:00"}, {"version": "3.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "d7c00c3000ac0ce79c96fcbfef86b49a71158cd1"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/d7c00c3000ac0ce79c96fcbfef86b49a71158cd1", "type": "zip", "shasum": "", "reference": "d7c00c3000ac0ce79c96fcbfef86b49a71158cd1"}, "time": "2017-12-19T21:44:46+00:00", "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0"}}, {"version": "3.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "4064632daf602552d40dcff30a1658ab76fc1621"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/4064632daf602552d40dcff30a1658ab76fc1621", "type": "zip", "shasum": "", "reference": "4064632daf602552d40dcff30a1658ab76fc1621"}, "time": "2017-12-18T00:24:01+00:00"}, {"version": "3.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "ba816f2e1bacc16278792c78b67c730dfff064a6"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/ba816f2e1bacc16278792c78b67c730dfff064a6", "type": "zip", "shasum": "", "reference": "ba816f2e1bacc16278792c78b67c730dfff064a6"}, "time": "2017-12-12T21:36:10+00:00"}, {"version": "3.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "d667e245d5dcd4d7bf80f26f2c947d476b66213e"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/d667e245d5dcd4d7bf80f26f2c947d476b66213e", "type": "zip", "shasum": "", "reference": "d667e245d5dcd4d7bf80f26f2c947d476b66213e"}, "time": "2017-10-16T22:40:25+00:00"}, {"version": "3.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "3c2d0a0fe39684ba0c1eb842a6a775d0b938d699"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/3c2d0a0fe39684ba0c1eb842a6a775d0b938d699", "type": "zip", "shasum": "", "reference": "3c2d0a0fe39684ba0c1eb842a6a775d0b938d699"}, "time": "2017-09-19T22:47:14+00:00"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "c7594a88ae75401e8f8d0bd4deb8431b39045c51"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/c7594a88ae75401e8f8d0bd4deb8431b39045c51", "type": "zip", "shasum": "", "reference": "c7594a88ae75401e8f8d0bd4deb8431b39045c51"}, "time": "2017-07-18T01:12:32+00:00", "require-dev": {"phpunit/phpunit": "~4.0"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "f9eaf037edf22fdfccf04cb0ab57ebcb1e166219"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/f9eaf037edf22fdfccf04cb0ab57ebcb1e166219", "type": "zip", "shasum": "", "reference": "f9eaf037edf22fdfccf04cb0ab57ebcb1e166219"}, "time": "2017-06-14T01:23:49+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "b95ff2c3b122a3ee4b57d149a57d2afce65522c3"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/b95ff2c3b122a3ee4b57d149a57d2afce65522c3", "type": "zip", "shasum": "", "reference": "b95ff2c3b122a3ee4b57d149a57d2afce65522c3"}, "time": "2017-05-04T00:33:04+00:00"}, {"version": "3.0.0RC4", "version_normalized": "*******-RC4", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "2d2bad4d946bf4a5a5399e1b1d1433212ee60035"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/2d2bad4d946bf4a5a5399e1b1d1433212ee60035", "type": "zip", "shasum": "", "reference": "2d2bad4d946bf4a5a5399e1b1d1433212ee60035"}, "time": "2017-03-01T22:32:23+00:00"}, {"version": "3.0.0RC3", "version_normalized": "*******-RC3", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "2c1ba4635457a793238982d3ea21a20b87863330"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/2c1ba4635457a793238982d3ea21a20b87863330", "type": "zip", "shasum": "", "reference": "2c1ba4635457a793238982d3ea21a20b87863330"}, "time": "2017-02-02T03:53:25+00:00", "autoload": {"files": ["autoload.php"], "psr-4": {"PHP_CodeSniffer\\": "src/", "PHP_CodeSniffer\\Tests\\": "tests/"}}}, {"version": "3.0.0RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "88509472ad27b168e1b551552df2c30bd6048471"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/88509472ad27b168e1b551552df2c30bd6048471", "type": "zip", "shasum": "", "reference": "88509472ad27b168e1b551552df2c30bd6048471"}, "time": "2016-11-30T04:23:58+00:00"}, {"version": "3.0.0RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "3d91716aa4e666bcbf8c46f9203e59c7a2a1ac56"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/3d91716aa4e666bcbf8c46f9203e59c7a2a1ac56", "type": "zip", "shasum": "", "reference": "3d91716aa4e666bcbf8c46f9203e59c7a2a1ac56"}, "time": "2016-09-02T00:30:35+00:00"}, {"version": "3.0.0a1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "5c6cffc3a8bcb745e4e70e89eee5f4ae9a6087f9"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/5c6cffc3a8bcb745e4e70e89eee5f4ae9a6087f9", "type": "zip", "shasum": "", "reference": "5c6cffc3a8bcb745e4e70e89eee5f4ae9a6087f9"}, "time": "2016-07-20T03:32:55+00:00"}, {"version": "2.9.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "2acf168de78487db620ab4bc524135a13cfe6745"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/2acf168de78487db620ab4bc524135a13cfe6745", "type": "zip", "shasum": "", "reference": "2acf168de78487db620ab4bc524135a13cfe6745"}, "time": "2018-11-07T22:31:41+00:00", "autoload": {"classmap": ["CodeSniffer.php", "CodeSniffer/CLI.php", "CodeSniffer/Exception.php", "CodeSniffer/File.php", "CodeSniffer/Fixer.php", "CodeSniffer/Report.php", "CodeSniffer/Reporting.php", "CodeSniffer/Sniff.php", "CodeSniffer/Tokens.php", "CodeSniffer/Reports/", "CodeSniffer/Tokenizers/", "CodeSniffer/DocGenerators/", "CodeSniffer/Standards/AbstractPatternSniff.php", "CodeSniffer/Standards/AbstractScopeSniff.php", "CodeSniffer/Standards/AbstractVariableSniff.php", "CodeSniffer/Standards/IncorrectPatternException.php", "CodeSniffer/Standards/Generic/Sniffs/", "CodeSniffer/Standards/MySource/Sniffs/", "CodeSniffer/Standards/PEAR/Sniffs/", "CodeSniffer/Standards/PSR1/Sniffs/", "CodeSniffer/Standards/PSR2/Sniffs/", "CodeSniffer/Standards/Squiz/Sniffs/", "CodeSniffer/Standards/Zend/Sniffs/"]}, "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "bin": ["scripts/phpcs", "scripts/phpcbf"], "require": {"php": ">=5.1.2", "ext-tokenizer": "*", "ext-xmlwriter": "*", "ext-simplexml": "*"}}, {"version": "2.9.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "dcbed1074f8244661eecddfc2a675430d8d33f62"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/dcbed1074f8244661eecddfc2a675430d8d33f62", "type": "zip", "shasum": "", "reference": "dcbed1074f8244661eecddfc2a675430d8d33f62"}, "time": "2017-05-22T02:43:20+00:00"}, {"version": "2.9.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "f7dfecbee89d68ab475a6c9e17d22bc9b69aed97"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/f7dfecbee89d68ab475a6c9e17d22bc9b69aed97", "type": "zip", "shasum": "", "reference": "f7dfecbee89d68ab475a6c9e17d22bc9b69aed97"}, "time": "2017-05-03T23:30:39+00:00"}, {"version": "2.8.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "d7cf0d894e8aa4c73712ee4a331cc1eaa37cdc7d"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/d7cf0d894e8aa4c73712ee4a331cc1eaa37cdc7d", "type": "zip", "shasum": "", "reference": "d7cf0d894e8aa4c73712ee4a331cc1eaa37cdc7d"}, "time": "2017-03-01T22:17:45+00:00"}, {"version": "2.8.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "86dd55a522238211f9f3631e3361703578941d9a"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/86dd55a522238211f9f3631e3361703578941d9a", "type": "zip", "shasum": "", "reference": "86dd55a522238211f9f3631e3361703578941d9a"}, "time": "2017-02-02T03:30:00+00:00"}, {"version": "2.7.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "9b324f3a1132459a7274a0ace2e1b766ba80930f"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/9b324f3a1132459a7274a0ace2e1b766ba80930f", "type": "zip", "shasum": "", "reference": "9b324f3a1132459a7274a0ace2e1b766ba80930f"}, "time": "2016-11-30T04:02:31+00:00"}, {"version": "2.7.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "571e27b6348e5b3a637b2abc82ac0d01e6d7bbed"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/571e27b6348e5b3a637b2abc82ac0d01e6d7bbed", "type": "zip", "shasum": "", "reference": "571e27b6348e5b3a637b2abc82ac0d01e6d7bbed"}, "time": "2016-09-01T23:53:02+00:00"}, {"version": "2.6.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "4edb770cb853def6e60c93abb088ad5ac2010c83"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/4edb770cb853def6e60c93abb088ad5ac2010c83", "type": "zip", "shasum": "", "reference": "4edb770cb853def6e60c93abb088ad5ac2010c83"}, "time": "2016-07-13T23:29:13+00:00"}, {"version": "2.6.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "fb72ed32f8418db5e7770be1653e62e0d6f5dd3d"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/fb72ed32f8418db5e7770be1653e62e0d6f5dd3d", "type": "zip", "shasum": "", "reference": "fb72ed32f8418db5e7770be1653e62e0d6f5dd3d"}, "time": "2016-05-30T22:24:32+00:00"}, {"version": "2.6.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "1bcdf03b068a530ac1962ce671dead356eeba43b"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/1bcdf03b068a530ac1962ce671dead356eeba43b", "type": "zip", "shasum": "", "reference": "1bcdf03b068a530ac1962ce671dead356eeba43b"}, "time": "2016-04-03T22:58:34+00:00"}, {"version": "2.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "6731851d6aaf1d0d6c58feff1065227b7fda3ba8"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/6731851d6aaf1d0d6c58feff1065227b7fda3ba8", "type": "zip", "shasum": "", "reference": "6731851d6aaf1d0d6c58feff1065227b7fda3ba8"}, "time": "2016-01-19T23:39:10+00:00", "require": {"php": ">=5.1.2", "ext-tokenizer": "*", "ext-xmlwriter": "*"}}, {"version": "2.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "e4fb41d5d0387d556e2c25534d630b3cce90ea67"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/e4fb41d5d0387d556e2c25534d630b3cce90ea67", "type": "zip", "shasum": "", "reference": "e4fb41d5d0387d556e2c25534d630b3cce90ea67"}, "time": "2015-12-11T00:12:46+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}}, {"version": "2.4.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "32a879f4f35019d78d568db2885d7779ca084a33"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/32a879f4f35019d78d568db2885d7779ca084a33", "type": "zip", "shasum": "", "reference": "32a879f4f35019d78d568db2885d7779ca084a33"}, "time": "2015-11-23T21:30:59+00:00", "require-dev": "__unset"}, {"version": "2.3.4", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "11a2545c44a5915f883e2e5ec12e14ed345e3ab2"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/11a2545c44a5915f883e2e5ec12e14ed345e3ab2", "type": "zip", "shasum": "", "reference": "11a2545c44a5915f883e2e5ec12e14ed345e3ab2"}, "time": "2015-09-09T00:18:50+00:00"}, {"version": "2.3.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "c1a26c729508f73560c1a4f767f60b8ab6b4a666"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/c1a26c729508f73560c1a4f767f60b8ab6b4a666", "type": "zip", "shasum": "", "reference": "c1a26c729508f73560c1a4f767f60b8ab6b4a666"}, "time": "2015-06-24T03:16:23+00:00"}, {"version": "2.3.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "e96d8579fbed0c95ecf2a0501ec4f307a4aa6404"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/e96d8579fbed0c95ecf2a0501ec4f307a4aa6404", "type": "zip", "shasum": "", "reference": "e96d8579fbed0c95ecf2a0501ec4f307a4aa6404"}, "time": "2015-04-28T23:28:20+00:00"}, {"version": "2.3.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "f3100143e94bbeeaa4f1cd7c6389c3733d3d1ce1"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/f3100143e94bbeeaa4f1cd7c6389c3733d3d1ce1", "type": "zip", "shasum": "", "reference": "f3100143e94bbeeaa4f1cd7c6389c3733d3d1ce1"}, "time": "2015-04-23T03:40:59+00:00"}, {"version": "2.3.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "5046b0e01c416fc2b06df961d0673c85bcdc896c"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/5046b0e01c416fc2b06df961d0673c85bcdc896c", "type": "zip", "shasum": "", "reference": "5046b0e01c416fc2b06df961d0673c85bcdc896c"}, "time": "2015-03-04T02:07:03+00:00"}, {"version": "2.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "b301c98f19414d836fdaa678648745fcca5aeb4f"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/b301c98f19414d836fdaa678648745fcca5aeb4f", "type": "zip", "shasum": "", "reference": "b301c98f19414d836fdaa678648745fcca5aeb4f"}, "time": "2015-01-21T22:44:05+00:00", "require": {"php": ">=5.1.2", "ext-tokenizer": "*"}, "extra": "__unset"}, {"description": "PHP_CodeSniffer tokenises PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "version": "2.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "d2a1d4c58fd2bb09ba376d0d19e67c0ab649e401"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/d2a1d4c58fd2bb09ba376d0d19e67c0ab649e401", "type": "zip", "shasum": "", "reference": "d2a1d4c58fd2bb09ba376d0d19e67c0ab649e401"}, "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name%5B%5D=PHP_CodeSniffer", "source": "https://github.com/squizlabs/PHP_CodeSniffer"}, "time": "2014-12-18T02:37:51+00:00", "extra": {"branch-alias": {"dev-phpcs-fixer": "2.0.x-dev"}}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "5b63fd3fe960e2020770c845e04254e5ca2b722a"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/5b63fd3fe960e2020770c845e04254e5ca2b722a", "type": "zip", "shasum": "", "reference": "5b63fd3fe960e2020770c845e04254e5ca2b722a"}, "time": "2014-12-05T00:14:12+00:00"}, {"version": "2.0.0RC4", "version_normalized": "*******-RC4", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "bf803199c1a0973dee174f06cf0366ed2c9710b0"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/bf803199c1a0973dee174f06cf0366ed2c9710b0", "type": "zip", "shasum": "", "reference": "bf803199c1a0973dee174f06cf0366ed2c9710b0"}, "time": "2014-11-06T21:18:20+00:00"}, {"version": "2.0.0RC3", "version_normalized": "*******-RC3", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "995d6e97f79b5f15631e2082ee6eb80279d114f2"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/995d6e97f79b5f15631e2082ee6eb80279d114f2", "type": "zip", "shasum": "", "reference": "995d6e97f79b5f15631e2082ee6eb80279d114f2"}, "time": "2014-10-15T23:54:48+00:00"}, {"version": "2.0.0RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "ecfb4ed8e45469338292dbfe9b1c2204a71ebdb0"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/ecfb4ed8e45469338292dbfe9b1c2204a71ebdb0", "type": "zip", "shasum": "", "reference": "ecfb4ed8e45469338292dbfe9b1c2204a71ebdb0"}, "time": "2014-09-25T22:43:48+00:00"}, {"version": "2.0.0RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "4a1919c1a96cea0d795977c118eed04057b5c962"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/4a1919c1a96cea0d795977c118eed04057b5c962", "type": "zip", "shasum": "", "reference": "4a1919c1a96cea0d795977c118eed04057b5c962"}, "time": "2014-08-06T00:02:42+00:00"}, {"version": "2.0.0a2", "version_normalized": "*******-alpha2", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "f89732c7aa3d4cf56d142a78b56eb1b20ffc54f5"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/f89732c7aa3d4cf56d142a78b56eb1b20ffc54f5", "type": "zip", "shasum": "", "reference": "f89732c7aa3d4cf56d142a78b56eb1b20ffc54f5"}, "time": "2014-05-01T03:26:02+00:00"}, {"version": "2.0.0a1", "version_normalized": "*******-alpha1", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "1e62933ec947186a542f3dbc14b98e0c92a2e7a2"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/1e62933ec947186a542f3dbc14b98e0c92a2e7a2", "type": "zip", "shasum": "", "reference": "1e62933ec947186a542f3dbc14b98e0c92a2e7a2"}, "time": "2014-02-05T02:56:59+00:00", "autoload": {"classmap": ["CodeSniffer.php", "CodeSniffer/CLI.php", "CodeSniffer/Exception.php", "CodeSniffer/File.php", "CodeSniffer/Fixer.php", "CodeSniffer/Report.php", "CodeSniffer/Reporting.php", "CodeSniffer/Sniff.php", "CodeSniffer/Tokens.php", "CodeSniffer/Reports/", "CodeSniffer/CommentParser/", "CodeSniffer/Tokenizers/", "CodeSniffer/DocGenerators/", "CodeSniffer/Standards/AbstractPatternSniff.php", "CodeSniffer/Standards/AbstractScopeSniff.php", "CodeSniffer/Standards/AbstractVariableSniff.php", "CodeSniffer/Standards/IncorrectPatternException.php", "CodeSniffer/Standards/Generic/Sniffs/", "CodeSniffer/Standards/MySource/Sniffs/", "CodeSniffer/Standards/PEAR/Sniffs/", "CodeSniffer/Standards/PSR1/Sniffs/", "CodeSniffer/Standards/PSR2/Sniffs/", "CodeSniffer/Standards/Squiz/Sniffs/", "CodeSniffer/Standards/Zend/Sniffs/"]}, "suggest": {"phpunit/php-timer": "dev-master"}, "extra": "__unset"}, {"version": "1.5.6", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "6f3e42d311b882b25b4d409d23a289f4d3b803d5"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/6f3e42d311b882b25b4d409d23a289f4d3b803d5", "type": "zip", "shasum": "", "reference": "6f3e42d311b882b25b4d409d23a289f4d3b803d5"}, "time": "2014-12-04T22:32:15+00:00", "autoload": {"classmap": ["CodeSniffer.php", "CodeSniffer/CLI.php", "CodeSniffer/Exception.php", "CodeSniffer/File.php", "CodeSniffer/Report.php", "CodeSniffer/Reporting.php", "CodeSniffer/Sniff.php", "CodeSniffer/Tokens.php", "CodeSniffer/Reports/", "CodeSniffer/CommentParser/", "CodeSniffer/Tokenizers/", "CodeSniffer/DocGenerators/", "CodeSniffer/Standards/AbstractPatternSniff.php", "CodeSniffer/Standards/AbstractScopeSniff.php", "CodeSniffer/Standards/AbstractVariableSniff.php", "CodeSniffer/Standards/IncorrectPatternException.php", "CodeSniffer/Standards/Generic/Sniffs/", "CodeSniffer/Standards/MySource/Sniffs/", "CodeSniffer/Standards/PEAR/Sniffs/", "CodeSniffer/Standards/PSR1/Sniffs/", "CodeSniffer/Standards/PSR2/Sniffs/", "CodeSniffer/Standards/Squiz/Sniffs/", "CodeSniffer/Standards/Zend/Sniffs/"]}, "extra": {"branch-alias": {"dev-phpcs-fixer": "2.0.x-dev"}}, "bin": ["scripts/phpcs"]}, {"version": "1.5.5", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "5d973e59cf58a0c847f298de84374c96b42b17b3"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/5d973e59cf58a0c847f298de84374c96b42b17b3", "type": "zip", "shasum": "", "reference": "5d973e59cf58a0c847f298de84374c96b42b17b3"}, "time": "2014-09-25T03:33:46+00:00"}, {"version": "1.5.4", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "4097e2c106e4a32bc234ae880e5585a19137e435"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/4097e2c106e4a32bc234ae880e5585a19137e435", "type": "zip", "shasum": "", "reference": "4097e2c106e4a32bc234ae880e5585a19137e435"}, "time": "2014-08-05T23:54:05+00:00"}, {"version": "1.5.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "396178ada8499ec492363587f037125bf7b07fcc"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/396178ada8499ec492363587f037125bf7b07fcc", "type": "zip", "shasum": "", "reference": "396178ada8499ec492363587f037125bf7b07fcc"}, "time": "2014-05-01T03:07:07+00:00"}, {"version": "1.5.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "a76a39b317ce8106abe6264daa505e24e1731860"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/a76a39b317ce8106abe6264daa505e24e1731860", "type": "zip", "shasum": "", "reference": "a76a39b317ce8106abe6264daa505e24e1731860"}, "time": "2014-02-04T23:49:58+00:00", "extra": "__unset"}, {"version": "1.5.1", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "bd1e50b8c252c914881dba1dcef089219e430dbd"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/bd1e50b8c252c914881dba1dcef089219e430dbd", "type": "zip", "shasum": "", "reference": "bd1e50b8c252c914881dba1dcef089219e430dbd"}, "time": "2013-12-12T03:08:49+00:00"}, {"version": "1.5.0", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "0191c4f193a62915227ac7946b1600c82cc3c253"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/0191c4f193a62915227ac7946b1600c82cc3c253", "type": "zip", "shasum": "", "reference": "0191c4f193a62915227ac7946b1600c82cc3c253"}, "time": "2013-11-27T22:22:42+00:00"}, {"version": "1.5.0RC4", "version_normalized": "*******-RC4", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "146a9b54e4adeaca0a3ae073e0a8a03570d6cc43"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/146a9b54e4adeaca0a3ae073e0a8a03570d6cc43", "type": "zip", "shasum": "", "reference": "146a9b54e4adeaca0a3ae073e0a8a03570d6cc43"}, "time": "2013-09-26T00:14:02+00:00"}, {"version": "1.5.0RC3", "version_normalized": "*******-RC3", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "9bcf8d2f59d7609766789cdecec96376d690180e"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/9bcf8d2f59d7609766789cdecec96376d690180e", "type": "zip", "shasum": "", "reference": "9bcf8d2f59d7609766789cdecec96376d690180e"}, "time": "2013-07-25T04:12:54+00:00"}, {"version": "1.5.0RC2", "version_normalized": "*******-RC2", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "3f6d43603548200ae26acc94b37286929a27b0a1"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/3f6d43603548200ae26acc94b37286929a27b0a1", "type": "zip", "shasum": "", "reference": "3f6d43603548200ae26acc94b37286929a27b0a1"}, "time": "2013-04-03T23:23:03+00:00", "autoload": {"classmap": ["CodeSniffer.php", "CodeSniffer/CLI.php", "CodeSniffer/Exception.php", "CodeSniffer/File.php", "CodeSniffer/MultiFileSniff.php", "CodeSniffer/Report.php", "CodeSniffer/Reporting.php", "CodeSniffer/Sniff.php", "CodeSniffer/Tokens.php", "CodeSniffer/Reports/", "CodeSniffer/CommentParser/", "CodeSniffer/Tokenizers/", "CodeSniffer/DocGenerators/", "CodeSniffer/Standards/AbstractPatternSniff.php", "CodeSniffer/Standards/AbstractScopeSniff.php", "CodeSniffer/Standards/AbstractVariableSniff.php", "CodeSniffer/Standards/IncorrectPatternException.php", "CodeSniffer/Standards/Generic/Sniffs/", "CodeSniffer/Standards/MySource/Sniffs/", "CodeSniffer/Standards/PEAR/Sniffs/", "CodeSniffer/Standards/PSR1/Sniffs/", "CodeSniffer/Standards/PSR2/Sniffs/", "CodeSniffer/Standards/Squiz/Sniffs/", "CodeSniffer/Standards/Zend/Sniffs/"]}, "require": {"php": ">=5.1.2"}, "suggest": "__unset"}, {"version": "1.5.0RC1", "version_normalized": "*******-RC1", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "526a866d2f5ac11f11dcf0b5bf947f37c702e2ac"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/526a866d2f5ac11f11dcf0b5bf947f37c702e2ac", "type": "zip", "shasum": "", "reference": "526a866d2f5ac11f11dcf0b5bf947f37c702e2ac"}, "time": "2013-02-08T03:21:31+00:00"}, {"version": "1.4.8", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "d26daa8096ad2c8758677f0352597f8cda4722e0"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/d26daa8096ad2c8758677f0352597f8cda4722e0", "type": "zip", "shasum": "", "reference": "d26daa8096ad2c8758677f0352597f8cda4722e0"}, "time": "2013-11-25T22:07:04+00:00", "require": {"php": ">=5.1.2", "ext-tokenizer": "*"}, "suggest": {"phpunit/php-timer": "dev-master"}}, {"version": "1.4.7", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "df764a1604607ea61c4eeea5410666e27cb37a8b"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/df764a1604607ea61c4eeea5410666e27cb37a8b", "type": "zip", "shasum": "", "reference": "df764a1604607ea61c4eeea5410666e27cb37a8b"}, "time": "2013-09-26T00:08:21+00:00"}, {"version": "1.4.6", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "a99caa52ed173ee83d6721ba012614f38c1dcbb6"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/a99caa52ed173ee83d6721ba012614f38c1dcbb6", "type": "zip", "shasum": "", "reference": "a99caa52ed173ee83d6721ba012614f38c1dcbb6"}, "time": "2013-07-25T03:49:02+00:00"}, {"version": "1.4.5", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "44f01e18f7b7d5b4301fd86dd1762a23fc3ccd06"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/44f01e18f7b7d5b4301fd86dd1762a23fc3ccd06", "type": "zip", "shasum": "", "reference": "44f01e18f7b7d5b4301fd86dd1762a23fc3ccd06"}, "time": "2013-04-03T23:16:28+00:00", "require": {"php": ">=5.1.2"}, "suggest": "__unset"}, {"version": "1.4.4", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "579b05a6f107126a790848cc2e74eee7ef618910"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/579b05a6f107126a790848cc2e74eee7ef618910", "type": "zip", "shasum": "", "reference": "579b05a6f107126a790848cc2e74eee7ef618910"}, "time": "2013-02-06T23:11:59+00:00"}, {"version": "1.4.3", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "bef13221b72f7a40315d06d25c4789a24c78fb6f"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/bef13221b72f7a40315d06d25c4789a24c78fb6f", "type": "zip", "shasum": "", "reference": "bef13221b72f7a40315d06d25c4789a24c78fb6f"}, "time": "2012-12-04T03:15:31+00:00", "autoload": {"classmap": ["."]}}, {"version": "1.4.2", "version_normalized": "*******", "source": {"url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "type": "git", "reference": "2e698c3fc4b73c67ad1e21245b0ab507b1847aab"}, "dist": {"url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/2e698c3fc4b73c67ad1e21245b0ab507b1847aab", "type": "zip", "shasum": "", "reference": "2e698c3fc4b73c67ad1e21245b0ab507b1847aab"}, "time": "2012-11-09T02:33:21+00:00", "autoload": "__unset"}]}, "security-advisories": [{"advisoryId": "PKSA-6vdd-n4sx-knhy", "affectedVersions": ">=1.0.0,<2.0.0|>=2.0.0,<2.8.1"}, {"advisoryId": "PKSA-6k8z-x1k1-jxhg", "affectedVersions": ">=3.0.0,<3.0.1"}], "last-modified": "Fri, 05 Sep 2025 05:52:16 GMT"}