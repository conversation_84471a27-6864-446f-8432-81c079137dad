{"minified": "composer/2.0", "packages": {"sebastian/recursion-context": [{"name": "sebastian/recursion-context", "description": "Provides functionality to recursively process PHP variables", "keywords": [], "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "version": "7.0.1", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "0b01998a7d5b1f122911a66bebcb8d46f0c82d8c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/0b01998a7d5b1f122911a66bebcb8d46f0c82d8c", "type": "zip", "shasum": "", "reference": "0b01998a7d5b1f122911a66bebcb8d46f0c82d8c"}, "type": "library", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/7.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/recursion-context", "type": "tidelift"}], "time": "2025-08-13T04:44:59+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}}, {"version": "7.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "c405ae3a63e01b32eb71577f8ec1604e39858a7c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/c405ae3a63e01b32eb71577f8ec1604e39858a7c", "type": "zip", "shasum": "", "reference": "c405ae3a63e01b32eb71577f8ec1604e39858a7c"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/7.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T05:00:01+00:00"}, {"version": "6.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "f6458abbf32a6c8174f8f26261475dc133b3d9dc"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/f6458abbf32a6c8174f8f26261475dc133b3d9dc", "type": "zip", "shasum": "", "reference": "f6458abbf32a6c8174f8f26261475dc133b3d9dc"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/6.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/recursion-context", "type": "tidelift"}], "time": "2025-08-13T04:42:22+00:00", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.3"}}, {"version": "6.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "694d156164372abbd149a4b85ccda2e4670c0e16"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/694d156164372abbd149a4b85ccda2e4670c0e16", "type": "zip", "shasum": "", "reference": "694d156164372abbd149a4b85ccda2e4670c0e16"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-07-03T05:10:34+00:00", "require-dev": {"phpunit/phpunit": "^11.0"}}, {"version": "6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "2f15508e17af4ea35129bbc32ce28a814d9c7426"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/2f15508e17af4ea35129bbc32ce28a814d9c7426", "type": "zip", "shasum": "", "reference": "2f15508e17af4ea35129bbc32ce28a814d9c7426"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/6.0.1"}, "time": "2024-06-17T05:22:57+00:00"}, {"version": "6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "b75224967b5a466925c6d54e68edd0edf8dd4ed4"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/recursion-context/zipball/b75224967b5a466925c6d54e68edd0edf8dd4ed4", "type": "zip", "shasum": "", "reference": "b75224967b5a466925c6d54e68edd0edf8dd4ed4"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/6.0.0"}, "time": "2024-02-02T06:08:48+00:00"}, {"version": "5.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "47e34210757a2f37a97dcd207d032e1b01e64c7a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/47e34210757a2f37a97dcd207d032e1b01e64c7a", "type": "zip", "shasum": "", "reference": "47e34210757a2f37a97dcd207d032e1b01e64c7a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/5.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/recursion-context", "type": "tidelift"}], "time": "2025-08-10T07:50:56+00:00", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.5"}}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "05909fb5bc7df4c52992396d0116aed689f93712"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/05909fb5bc7df4c52992396d0116aed689f93712", "type": "zip", "shasum": "", "reference": "05909fb5bc7df4c52992396d0116aed689f93712"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:05:40+00:00", "require-dev": {"phpunit/phpunit": "^10.0"}}, {"version": "4.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "539c6691e0623af6dc6f9c20384c120f963465a0"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/539c6691e0623af6dc6f9c20384c120f963465a0", "type": "zip", "shasum": "", "reference": "539c6691e0623af6dc6f9c20384c120f963465a0"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/recursion-context", "type": "tidelift"}], "time": "2025-08-10T06:57:39+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}}, {"version": "4.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "type": "zip", "shasum": "", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:07:39+00:00"}, {"homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "version": "4.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "cd9d8cf3c5804de4341c283ed787f099f5506172"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/cd9d8cf3c5804de4341c283ed787f099f5506172", "type": "zip", "shasum": "", "reference": "cd9d8cf3c5804de4341c283ed787f099f5506172"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.4"}, "time": "2020-10-26T13:17:30+00:00"}, {"version": "4.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "ed8c9cd355089134bc9cba421b5cfdd58f0eaef7"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/ed8c9cd355089134bc9cba421b5cfdd58f0eaef7", "type": "zip", "shasum": "", "reference": "ed8c9cd355089134bc9cba421b5cfdd58f0eaef7"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.3"}, "time": "2020-09-28T05:17:32+00:00"}, {"version": "4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "062231bf61d2b9448c4fa5a7643b5e1829c11d63"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/062231bf61d2b9448c4fa5a7643b5e1829c11d63", "type": "zip", "shasum": "", "reference": "062231bf61d2b9448c4fa5a7643b5e1829c11d63"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/master"}, "time": "2020-06-26T12:14:17+00:00", "require": {"php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "a32789e5f0157c10cf216ce6c5136db12a12b847"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/a32789e5f0157c10cf216ce6c5136db12a12b847", "type": "zip", "shasum": "", "reference": "a32789e5f0157c10cf216ce6c5136db12a12b847"}, "time": "2020-06-15T13:06:44+00:00", "require": {"php": "^7.3"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "cdd86616411fc3062368b720b0425de10bd3d579"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/cdd86616411fc3062368b720b0425de10bd3d579", "type": "zip", "shasum": "", "reference": "cdd86616411fc3062368b720b0425de10bd3d579"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.0"}, "time": "2020-02-07T06:18:20+00:00", "funding": "__unset"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "8fe7e75986a9d24b4cceae847314035df7703a5a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/8fe7e75986a9d24b4cceae847314035df7703a5a", "type": "zip", "shasum": "", "reference": "8fe7e75986a9d24b4cceae847314035df7703a5a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/recursion-context", "type": "tidelift"}], "time": "2025-08-10T05:25:53+00:00", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "9bfd3c6f1f08c026f542032dfb42813544f7d64c"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/9bfd3c6f1f08c026f542032dfb42813544f7d64c", "type": "zip", "shasum": "", "reference": "9bfd3c6f1f08c026f542032dfb42813544f7d64c"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T14:07:30+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/367dcba38d6e1977be014dc4b22f47a484dac7fb", "type": "zip", "shasum": "", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/3.0.1"}, "time": "2020-11-30T07:34:24+00:00"}, {"version": "3.0.0", "version_normalized": "*******", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "type": "zip", "shasum": "", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/master"}, "time": "2017-03-03T06:23:57+00:00", "require": {"php": "^7.0"}, "funding": "__unset"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/2c3ba150cbec723aa057506e73a8d33bdb286c9a", "type": "zip", "shasum": "", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a"}, "time": "2016-11-19T07:33:16+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "b19cc3298482a335a95f3016d2f8a6950f0fbcd7"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/b19cc3298482a335a95f3016d2f8a6950f0fbcd7", "type": "zip", "shasum": "", "reference": "b19cc3298482a335a95f3016d2f8a6950f0fbcd7"}, "time": "2016-10-03T07:41:43+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "913401df809e99e4f47b27cdd781f4a258d58791"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/913401df809e99e4f47b27cdd781f4a258d58791", "type": "zip", "shasum": "", "reference": "913401df809e99e4f47b27cdd781f4a258d58791"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/1.0.2"}, "time": "2015-11-11T19:50:13+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "994d4a811bafe801fb06dccbee797863ba2792ba"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/994d4a811bafe801fb06dccbee797863ba2792ba", "type": "zip", "shasum": "", "reference": "994d4a811bafe801fb06dccbee797863ba2792ba"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/master"}, "time": "2015-06-21T08:04:50+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "type": "git", "reference": "3989662bbb30a29d20d9faa04a846af79b276252"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/recursion-context/zipball/3989662bbb30a29d20d9faa04a846af79b276252", "type": "zip", "shasum": "", "reference": "3989662bbb30a29d20d9faa04a846af79b276252"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/1.0.0"}, "time": "2015-01-24T09:48:32+00:00"}]}, "security-advisories": [], "last-modified": "Wed, 13 Aug 2025 04:45:14 GMT"}