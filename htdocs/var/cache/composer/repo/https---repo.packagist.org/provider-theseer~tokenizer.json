{"minified": "composer/2.0", "packages": {"theseer/tokenizer": [{"name": "theseer/tokenizer", "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "keywords": [], "homepage": "", "version": "1.2.3", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "source": {"url": "https://github.com/theseer/tokenizer.git", "type": "git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "type": "zip", "shasum": "", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "type": "library", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00", "autoload": {"classmap": ["src/"]}, "require": {"php": "^7.2 || ^8.0", "ext-xmlwriter": "*", "ext-dom": "*", "ext-tokenizer": "*"}}, {"version": "1.2.2", "version_normalized": "*******", "source": {"url": "https://github.com/theseer/tokenizer.git", "type": "git", "reference": "b2ad5003ca10d4ee50a12da31de12a5774ba6b96"}, "dist": {"url": "https://api.github.com/repos/theseer/tokenizer/zipball/b2ad5003ca10d4ee50a12da31de12a5774ba6b96", "type": "zip", "shasum": "", "reference": "b2ad5003ca10d4ee50a12da31de12a5774ba6b96"}, "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.2"}, "time": "2023-11-20T00:12:19+00:00"}, {"version": "1.2.1", "version_normalized": "*******", "source": {"url": "https://github.com/theseer/tokenizer.git", "type": "git", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e"}, "dist": {"url": "https://api.github.com/repos/theseer/tokenizer/zipball/34a41e998c2183e22995f158c581e7b5e755ab9e", "type": "zip", "shasum": "", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e"}, "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.1"}, "time": "2021-07-28T10:34:58+00:00"}, {"version": "1.2.0", "version_normalized": "*******", "source": {"url": "https://github.com/theseer/tokenizer.git", "type": "git", "reference": "75a63c33a8577608444246075ea0af0d052e452a"}, "dist": {"url": "https://api.github.com/repos/theseer/tokenizer/zipball/75a63c33a8577608444246075ea0af0d052e452a", "type": "zip", "shasum": "", "reference": "75a63c33a8577608444246075ea0af0d052e452a"}, "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/master"}, "time": "2020-07-12T23:59:07+00:00"}, {"version": "1.1.3", "version_normalized": "*******", "source": {"url": "https://github.com/theseer/tokenizer.git", "type": "git", "reference": "11336f6f84e16a720dae9d8e6ed5019efa85a0f9"}, "dist": {"url": "https://api.github.com/repos/theseer/tokenizer/zipball/11336f6f84e16a720dae9d8e6ed5019efa85a0f9", "type": "zip", "shasum": "", "reference": "11336f6f84e16a720dae9d8e6ed5019efa85a0f9"}, "time": "2019-06-13T22:48:21+00:00", "require": {"php": "^7.0", "ext-xmlwriter": "*", "ext-dom": "*", "ext-tokenizer": "*"}, "funding": "__unset"}, {"version": "1.1.2", "version_normalized": "*******", "source": {"url": "https://github.com/theseer/tokenizer.git", "type": "git", "reference": "1c42705be2b6c1de5904f8afacef5895cab44bf8"}, "dist": {"url": "https://api.github.com/repos/theseer/tokenizer/zipball/1c42705be2b6c1de5904f8afacef5895cab44bf8", "type": "zip", "shasum": "", "reference": "1c42705be2b6c1de5904f8afacef5895cab44bf8"}, "time": "2019-04-04T09:56:43+00:00"}, {"version": "1.1.1", "version_normalized": "*******", "source": {"url": "https://github.com/theseer/tokenizer.git", "type": "git", "reference": "06b6ce404ee574e9c1787fb67bb9980ca4387c34"}, "dist": {"url": "https://api.github.com/repos/theseer/tokenizer/zipball/06b6ce404ee574e9c1787fb67bb9980ca4387c34", "type": "zip", "shasum": "", "reference": "06b6ce404ee574e9c1787fb67bb9980ca4387c34"}, "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.1.1"}, "time": "2019-04-03T18:37:40+00:00"}, {"version": "1.1.0", "version_normalized": "*******", "source": {"url": "https://github.com/theseer/tokenizer.git", "type": "git", "reference": "cb2f008f3f05af2893a87208fe6a6c4985483f8b"}, "dist": {"url": "https://api.github.com/repos/theseer/tokenizer/zipball/cb2f008f3f05af2893a87208fe6a6c4985483f8b", "type": "zip", "shasum": "", "reference": "cb2f008f3f05af2893a87208fe6a6c4985483f8b"}, "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/master"}, "time": "2017-04-07T12:08:54+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/theseer/tokenizer.git", "type": "git", "reference": "de15c5f572425aa9b1e5d824b12625336939f32c"}, "dist": {"url": "https://api.github.com/repos/theseer/tokenizer/zipball/de15c5f572425aa9b1e5d824b12625336939f32c", "type": "zip", "shasum": "", "reference": "de15c5f572425aa9b1e5d824b12625336939f32c"}, "time": "2017-04-05T18:33:19+00:00"}]}, "security-advisories": [], "last-modified": "Thu, 18 Apr 2024 09:00:19 GMT"}