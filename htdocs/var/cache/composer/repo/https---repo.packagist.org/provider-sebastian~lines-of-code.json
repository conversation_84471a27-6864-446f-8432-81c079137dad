{"minified": "composer/2.0", "packages": {"sebastian/lines-of-code": [{"name": "sebastian/lines-of-code", "description": "Library for counting the lines of code in PHP source code", "keywords": [], "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "version": "4.0.0", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "type": "git", "reference": "97ffee3bcfb5805568d6af7f0f893678fc076d2f"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/lines-of-code/zipball/97ffee3bcfb5805568d6af7f0f893678fc076d2f", "type": "zip", "shasum": "", "reference": "97ffee3bcfb5805568d6af7f0f893678fc076d2f"}, "type": "library", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:57:28+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "require": {"php": ">=8.3", "nikic/php-parser": "^5.0"}, "require-dev": {"phpunit/phpunit": "^12.0"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "type": "git", "reference": "d36ad0d782e5756913e42ad87cb2890f4ffe467a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/d36ad0d782e5756913e42ad87cb2890f4ffe467a", "type": "zip", "shasum": "", "reference": "d36ad0d782e5756913e42ad87cb2890f4ffe467a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/3.0.1"}, "time": "2024-07-03T04:58:38+00:00", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "require": {"php": ">=8.2", "nikic/php-parser": "^5.0"}, "require-dev": {"phpunit/phpunit": "^11.0"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "type": "git", "reference": "376c5b3f6b43c78fdc049740bca76a7c846706c0"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/376c5b3f6b43c78fdc049740bca76a7c846706c0", "type": "zip", "shasum": "", "reference": "376c5b3f6b43c78fdc049740bca76a7c846706c0"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/3.0.0"}, "time": "2024-02-02T06:00:36+00:00"}, {"version": "2.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "type": "git", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/lines-of-code/zipball/856e7f6a75a84e339195d48c556f23be2ebf75d0", "type": "zip", "shasum": "", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/2.0.2"}, "time": "2023-12-21T08:38:20+00:00", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "require": {"php": ">=8.1", "nikic/php-parser": "^4.18 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "type": "git", "reference": "649e40d279e243d985aa8fb6e74dd5bb28dc185d"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/lines-of-code/zipball/649e40d279e243d985aa8fb6e74dd5bb28dc185d", "type": "zip", "shasum": "", "reference": "649e40d279e243d985aa8fb6e74dd5bb28dc185d"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/2.0.1"}, "time": "2023-08-31T09:25:50+00:00", "require": {"php": ">=8.1", "nikic/php-parser": "^4.10"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "type": "git", "reference": "17c4d940ecafb3d15d2cf916f4108f664e28b130"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/17c4d940ecafb3d15d2cf916f4108f664e28b130", "type": "zip", "shasum": "", "reference": "17c4d940ecafb3d15d2cf916f4108f664e28b130"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/2.0.0"}, "time": "2023-02-03T07:08:02+00:00"}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "type": "git", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/e1e4a170560925c26d424b6a03aed157e7dcc5c5", "type": "zip", "shasum": "", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.4"}, "time": "2023-12-22T06:20:34+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require": {"php": ">=7.3", "nikic/php-parser": "^4.18 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "type": "git", "reference": "c1c2e997aa3146983ed888ad08b15470a2e22ecc"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/c1c2e997aa3146983ed888ad08b15470a2e22ecc", "type": "zip", "shasum": "", "reference": "c1c2e997aa3146983ed888ad08b15470a2e22ecc"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.3"}, "time": "2020-11-28T06:42:11+00:00", "require": {"php": ">=7.3", "nikic/php-parser": "^4.6"}}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "type": "git", "reference": "acf76492a65401babcf5283296fa510782783a7a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/acf76492a65401babcf5283296fa510782783a7a", "type": "zip", "shasum": "", "reference": "acf76492a65401babcf5283296fa510782783a7a"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.2"}, "time": "2020-10-26T17:03:56+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "type": "git", "reference": "6514b8f21906b8b46f520d1fbd17a4523fa59a54"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/6514b8f21906b8b46f520d1fbd17a4523fa59a54", "type": "zip", "shasum": "", "reference": "6514b8f21906b8b46f520d1fbd17a4523fa59a54"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.1"}, "time": "2020-09-28T06:07:27+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "type": "git", "reference": "e02bf626f404b5daec382a7b8a6a4456e49017e5"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/e02bf626f404b5daec382a7b8a6a4456e49017e5", "type": "zip", "shasum": "", "reference": "e02bf626f404b5daec382a7b8a6a4456e49017e5"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.0"}, "time": "2020-07-22T18:33:42+00:00", "require": {"php": "^7.3 || ^8.0", "nikic/php-parser": "^4.6"}, "require-dev": {"phpunit/phpunit": "^9.2"}}]}, "security-advisories": [], "last-modified": "Fri, 07 Feb 2025 04:57:41 GMT"}