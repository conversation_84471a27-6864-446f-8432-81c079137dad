{"minified": "composer/2.0", "packages": {"sebastian/code-unit": [{"name": "sebastian/code-unit", "description": "Collection of value objects that represent the PHP code units", "keywords": [], "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "version": "3.0.3", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "54391c61e4af8078e5b276ab082b6d3c54c9ad64"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/54391c61e4af8078e5b276ab082b6d3c54c9ad64", "type": "zip", "shasum": "", "reference": "54391c61e4af8078e5b276ab082b6d3c54c9ad64"}, "type": "library", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-03-19T07:56:08+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.5"}}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "ee88b0cdbe74cf8dd3b54940ff17643c0d6543ca"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/ee88b0cdbe74cf8dd3b54940ff17643c0d6543ca", "type": "zip", "shasum": "", "reference": "ee88b0cdbe74cf8dd3b54940ff17643c0d6543ca"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/3.0.2"}, "time": "2024-12-12T09:59:06+00:00"}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "6bb7d09d6623567178cf54126afa9c2310114268"}, "dist": {"url": "https://api.github.com/repos/sebastian<PERSON>mann/code-unit/zipball/6bb7d09d6623567178cf54126afa9c2310114268", "type": "zip", "shasum": "", "reference": "6bb7d09d6623567178cf54126afa9c2310114268"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/3.0.1"}, "time": "2024-07-03T04:44:28+00:00", "require-dev": {"phpunit/phpunit": "^11.0"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "6634549cb8d702282a04a774e36a7477d2bd9015"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/6634549cb8d702282a04a774e36a7477d2bd9015", "type": "zip", "shasum": "", "reference": "6634549cb8d702282a04a774e36a7477d2bd9015"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/3.0.0"}, "time": "2024-02-02T05:50:41+00:00"}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/a81fee9eef0b7a76af11d121767abc44c104e503", "type": "zip", "shasum": "", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/2.0.0"}, "time": "2023-02-03T06:58:43+00:00", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}}, {"version": "1.0.8", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/1fc9f64c0927627ef78ba436c9b17d967e68e120", "type": "zip", "shasum": "", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.8"}, "time": "2020-10-26T13:08:54+00:00", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}}, {"version": "1.0.7", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "59236be62b1bb9919e6d7f60b0b832dc05cef9ab"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/59236be62b1bb9919e6d7f60b0b832dc05cef9ab", "type": "zip", "shasum": "", "reference": "59236be62b1bb9919e6d7f60b0b832dc05cef9ab"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.7"}, "time": "2020-10-02T14:47:54+00:00"}, {"version": "1.0.6", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "d3a241b6028ff9d8e97d2b6ebd4090d01f92fad8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/d3a241b6028ff9d8e97d2b6ebd4090d01f92fad8", "type": "zip", "shasum": "", "reference": "d3a241b6028ff9d8e97d2b6ebd4090d01f92fad8"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.6"}, "time": "2020-09-28T05:28:46+00:00"}, {"version": "1.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "c1e2df332c905079980b119c4db103117e5e5c90"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/c1e2df332c905079980b119c4db103117e5e5c90", "type": "zip", "shasum": "", "reference": "c1e2df332c905079980b119c4db103117e5e5c90"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/master"}, "time": "2020-06-26T12:50:45+00:00", "require": {"php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}}, {"version": "1.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "0e317f915db26c4206f59d8e7c04f358de8aee09"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/0e317f915db26c4206f59d8e7c04f358de8aee09", "type": "zip", "shasum": "", "reference": "0e317f915db26c4206f59d8e7c04f358de8aee09"}, "time": "2020-06-26T12:02:26+00:00"}, {"version": "1.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "d650ef9b1fece15ed4d6eaed6e6b469b7b81183a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/d650ef9b1fece15ed4d6eaed6e6b469b7b81183a", "type": "zip", "shasum": "", "reference": "d650ef9b1fece15ed4d6eaed6e6b469b7b81183a"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.3"}, "time": "2020-06-15T13:11:26+00:00", "require": {"php": "^7.3"}}, {"version": "1.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "ac958085bc19fcd1d36425c781ef4cbb5b06e2a5"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/ac958085bc19fcd1d36425c781ef4cbb5b06e2a5", "type": "zip", "shasum": "", "reference": "ac958085bc19fcd1d36425c781ef4cbb5b06e2a5"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.2"}, "time": "2020-04-30T05:58:10+00:00"}, {"version": "1.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "00d2094a93573796ec6666401be467fa6efcd86a"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/00d2094a93573796ec6666401be467fa6efcd86a", "type": "zip", "shasum": "", "reference": "00d2094a93573796ec6666401be467fa6efcd86a"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.1"}, "time": "2020-04-27T06:25:01+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "type": "git", "reference": "8d8f09bd47c75159921e6e84fdef146343962866"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/8d8f09bd47c75159921e6e84fdef146343962866", "type": "zip", "shasum": "", "reference": "8d8f09bd47c75159921e6e84fdef146343962866"}, "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/master"}, "time": "2020-03-30T11:59:20+00:00"}]}, "security-advisories": [], "last-modified": "Wed, 19 Mar 2025 07:56:38 GMT"}