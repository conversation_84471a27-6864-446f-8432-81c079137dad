{"minified": "composer/2.0", "packages": {"sebastian/object-enumerator": [{"name": "sebastian/object-enumerator", "description": "Traverses array structures and object graphs to enumerate all referenced objects", "keywords": [], "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "version": "7.0.0", "version_normalized": "*******", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "1effe8e9b8e068e9ae228e542d5d11b5d16db894"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/1effe8e9b8e068e9ae228e542d5d11b5d16db894", "type": "zip", "shasum": "", "reference": "1effe8e9b8e068e9ae228e542d5d11b5d16db894"}, "type": "library", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/7.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:57:48+00:00", "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "require": {"php": ">=8.3", "sebastian/object-reflector": "^5.0", "sebastian/recursion-context": "^7.0"}, "require-dev": {"phpunit/phpunit": "^12.0"}}, {"version": "6.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "f5b498e631a74204185071eb41f33f38d64608aa"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/f5b498e631a74204185071eb41f33f38d64608aa", "type": "zip", "shasum": "", "reference": "f5b498e631a74204185071eb41f33f38d64608aa"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/6.0.1"}, "time": "2024-07-03T05:00:13+00:00", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "require": {"php": ">=8.2", "sebastian/object-reflector": "^4.0", "sebastian/recursion-context": "^6.0"}, "require-dev": {"phpunit/phpunit": "^11.0"}}, {"version": "6.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "f75f6c460da0bbd9668f43a3dde0ec0ba7faa678"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/f75f6c460da0bbd9668f43a3dde0ec0ba7faa678", "type": "zip", "shasum": "", "reference": "f75f6c460da0bbd9668f43a3dde0ec0ba7faa678"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/6.0.0"}, "time": "2024-02-02T06:01:29+00:00"}, {"version": "5.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/202d0e344a580d7f7d04b3fafce6933e59dae906", "type": "zip", "shasum": "", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/5.0.0"}, "time": "2023-02-03T07:08:32+00:00", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}}, {"version": "4.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/5c9eeac41b290a3712d88851518825ad78f45c71", "type": "zip", "shasum": "", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.4"}, "time": "2020-10-26T13:12:34+00:00", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}}, {"version": "4.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "f6f5957013d84725427d361507e13513702888a4"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/f6f5957013d84725427d361507e13513702888a4", "type": "zip", "shasum": "", "reference": "f6f5957013d84725427d361507e13513702888a4"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.3"}, "time": "2020-09-28T05:55:06+00:00"}, {"version": "4.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "074fed2d0a6d08e1677dd8ce9d32aecb384917b8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/074fed2d0a6d08e1677dd8ce9d32aecb384917b8", "type": "zip", "shasum": "", "reference": "074fed2d0a6d08e1677dd8ce9d32aecb384917b8"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/master"}, "time": "2020-06-26T12:11:32+00:00", "require": {"php": "^7.3 || ^8.0", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}}, {"version": "4.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "15f319d67c49fc55ebcdbffb3377433125588455"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/15f319d67c49fc55ebcdbffb3377433125588455", "type": "zip", "shasum": "", "reference": "15f319d67c49fc55ebcdbffb3377433125588455"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.1"}, "time": "2020-06-15T13:15:25+00:00", "require": {"php": "^7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}}, {"version": "4.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "e67516b175550abad905dc952f43285957ef4363"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/e67516b175550abad905dc952f43285957ef4363", "type": "zip", "shasum": "", "reference": "e67516b175550abad905dc952f43285957ef4363"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/master"}, "time": "2020-02-07T06:12:23+00:00", "funding": "__unset"}, {"version": "3.0.5", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "ac5b293dba925751b808e02923399fb44ff0d541"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/ac5b293dba925751b808e02923399fb44ff0d541", "type": "zip", "shasum": "", "reference": "ac5b293dba925751b808e02923399fb44ff0d541"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/3.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:54:02+00:00", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "require": {"php": ">=7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}}, {"version": "3.0.4", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2"}, "dist": {"url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/object-enumerator/zipball/e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "type": "zip", "shasum": "", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/3.0.4"}, "time": "2020-11-30T07:40:27+00:00"}, {"version": "3.0.3", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/7cfd9e65d11ffb5af41198476395774d4c8a84c5", "type": "zip", "shasum": "", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5"}, "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/master"}, "time": "2017-08-03T12:35:26+00:00", "require": {"php": "^7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "funding": "__unset"}, {"version": "3.0.2", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "31dd3379d16446c5d86dec32ab1ad1f378581ad8"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/31dd3379d16446c5d86dec32ab1ad1f378581ad8", "type": "zip", "shasum": "", "reference": "31dd3379d16446c5d86dec32ab1ad1f378581ad8"}, "time": "2017-03-12T15:17:29+00:00", "require": {"php": "^7.0", "sebastian/object-reflector": "^1.0", "sebastian/recursion-context": "^3.0"}}, {"version": "3.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "601be3b8ddc75cc8c4e44e8f368c718c8f2d2f38"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/601be3b8ddc75cc8c4e44e8f368c718c8f2d2f38", "type": "zip", "shasum": "", "reference": "601be3b8ddc75cc8c4e44e8f368c718c8f2d2f38"}, "time": "2017-03-12T07:44:50+00:00", "require": {"php": "^7.0", "sebastian/recursion-context": "^3.0"}}, {"version": "3.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "de6e32f7192dfea2e4bedc892434f4830b5c5794"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/de6e32f7192dfea2e4bedc892434f4830b5c5794", "type": "zip", "shasum": "", "reference": "de6e32f7192dfea2e4bedc892434f4830b5c5794"}, "time": "2017-03-03T06:21:01+00:00"}, {"version": "2.0.1", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/1311872ac850040a79c3c058bea3e22d0f09cbb7", "type": "zip", "shasum": "", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7"}, "time": "2017-02-18T15:18:39+00:00", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "require": {"php": ">=5.6", "sebastian/recursion-context": "~2.0"}, "require-dev": {"phpunit/phpunit": "~5"}}, {"version": "2.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "96f8a3f257b69e8128ad74d3a7fd464bcbaa3b35"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/96f8a3f257b69e8128ad74d3a7fd464bcbaa3b35", "type": "zip", "shasum": "", "reference": "96f8a3f257b69e8128ad74d3a7fd464bcbaa3b35"}, "time": "2016-11-19T07:35:10+00:00"}, {"version": "1.0.0", "version_normalized": "*******", "source": {"url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "type": "git", "reference": "d4ca2fb70344987502567bc50081c03e6192fb26"}, "dist": {"url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/d4ca2fb70344987502567bc50081c03e6192fb26", "type": "zip", "shasum": "", "reference": "d4ca2fb70344987502567bc50081c03e6192fb26"}, "time": "2016-01-28T13:25:10+00:00", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "require": {"php": ">=5.6", "sebastian/recursion-context": "~1.0"}}]}, "security-advisories": [], "last-modified": "Fri, 07 Feb 2025 04:58:09 GMT"}