<{* Template for IPF password tray form element display *}>
<{* This template creates dual password fields for password confirmation *}>

<div class="password-tray">
    <{* First password field (from tray elements) *}>
    <{if $tray_elements}>
        <{foreach from=$tray_elements item=element}>
            <{$element}>
            <{if !$smarty.foreach.default.last}><{$tray_delimeter}><{/if}>
        <{/foreach}>
    <{/if}>
    
    <{* Second password field (confirmation) *}>
    <{if $second_password_field}>
        <{$tray_delimeter}>
        <{$second_password_field}>
    <{/if}>
</div>
