<!DOCTYPE html>
<html lang="en" x-data="cardSorter()" x-init="init()">
<head>
	<meta charset="UTF-8">
	<title>Multi-Template Drag-and-Drop Cards</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">

	<!-- <PERSON><PERSON>ma CSS -->
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">

	<!-- Alpine.js -->
	<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

	<!-- animate-css-grid (optional for layout animations) -->
	<script src="https://cdn.jsdelivr.net/npm/animate-css-grid@latest/dist/animate-css-grid.min.js"></script>

	<style>
		.card {
			cursor: grab;
			position: relative;
			transition: transform 0.2s ease, box-shadow 0.2s ease;
		}
		.card-wrapper {
			position: relative;
		}
		.ghost-card {
			border: 2px dashed #ccc;
			background-color: rgba(249, 249, 249, 0.8);
			height: 100%;
			min-height: 240px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: bold;
			color: #999;
		}
		.delete-btn {
			position: absolute;
			top: 8px;
			right: 8px;
			background: white;
			border: none;
			font-size: 1.2rem;
			cursor: pointer;
			z-index: 1;
		}
		.editable-title {
			cursor: text;
		}
		.image-preview {
			margin-top: 1rem;
			max-width: 300px;
		}
		.template-badge {
			background-color: #f5f5f5;
			border-left: 4px solid #3273dc;
			padding: 0.5rem;
			font-weight: bold;
		}
		.image-left {
			display: flex;
			gap: 1rem;
			align-items: center;
		}
		.image-left img {
			width: 100px;
			height: auto;
		}
	</style>
</head>
<body class="section">

<div class="container">
	<h1 class="title has-text-centered">Multi-Template Drag-and-Drop Cards</h1>

	<!-- Add Card Form -->
	<div class="box mb-5">
		<h2 class="subtitle">Add a New Card</h2>
		<div class="field">
			<label class="label">Title</label>
			<div class="control">
				<input class="input" type="text" x-model="newTitle" placeholder="Card title">
			</div>
		</div>
		<div class="field">
			<label class="label">Image URL</label>
			<div class="control">
				<input class="input" type="text" x-model="newImage" placeholder="https://example.com/image.jpg">
			</div>
		</div>
		<div class="field">
			<label class="label">Template</label>
			<div class="control">
				<div class="select">
					<select x-model="newTemplate">
						<option value="image-top">Image Top</option>
						<option value="image-left">Image Left</option>
						<option value="minimal">Minimal</option>
						<option value="badge">Badge</option>
					</select>
				</div>
			</div>
		</div>
		<template x-if="newImage && newImage.trim()">
			<figure class="image image-preview">
				<img :src="newImage" alt="Preview">
			</figure>
		</template>
		<div class="buttons mt-3">
			<button class="button is-primary" @click="addCard()">Add Card</button>
			<button class="button is-danger" @click="resetCards()">Reset All</button>
		</div>
		<p class="help">If Image URL is empty, a placeholder image will be used automatically.</p>
	</div>

	<!-- Cards with Ghosting and (optional) Animation -->
	<div class="columns is-multiline" x-ref="grid"
	     @dragover.prevent
	     @drop="handleDrop()">
		<template x-for="(card, index) in cardsWithGhost()" :key="card.id || 'ghost-' + index">
			<div class="column is-one-quarter">
				<template x-if="card.isGhost">
					<div class="card ghost-card">Drop here</div>
				</template>

				<template x-if="!card.isGhost">
					<div class="card-wrapper" @dragover.prevent="dragOver($event, index)">
						<div class="card"
						     draggable="true"
						     @dragstart="dragStart(index, $event)"
						     @dragend="dragEnd()">

							<!-- Template: image-top -->
							<template x-if="card.template === 'image-top'">
								<div>
									<div class="card-image">
										<figure class="image is-4by3">
											<img :src="card.image" :alt="card.title">
										</figure>
									</div>
									<div class="card-content">
										<input class="input editable-title"
										       x-model="card.title"
										       @click="$event.stopPropagation()"
										       @keydown.enter="$event.target.blur()"
										       @blur="saveCards()">
									</div>
								</div>
							</template>

							<!-- Template: image-left -->
							<template x-if="card.template === 'image-left'">
								<div class="card-content image-left">
									<img :src="card.image" :alt="card.title">
									<input class="input editable-title"
									       x-model="card.title"
									       @click="$event.stopPropagation()"
									       @keydown.enter="$event.target.blur()"
									       @blur="saveCards()">
								</div>
							</template>

							<!-- Template: minimal -->
							<template x-if="card.template === 'minimal'">
								<div class="card-content">
									<input class="input editable-title"
									       x-model="card.title"
									       @click="$event.stopPropagation()"
									       @keydown.enter="$event.target.blur()"
									       @blur="saveCards()">
								</div>
							</template>

							<!-- Template: badge -->
							<template x-if="card.template === 'badge'">
								<div class="card-content">
									<div class="template-badge">
										<input class="input editable-title"
										       x-model="card.title"
										       @click="$event.stopPropagation()"
										       @keydown.enter="$event.target.blur()"
										       @blur="saveCards()">
									</div>
								</div>
							</template>

							<button class="delete-btn" @click="removeCard(index)">×</button>
						</div>
					</div>
				</template>
			</div>
		</template>
	</div>
</div>

<script>
	function cardSorter() {
		return {
			// State
			cards: [],
			draggedIndex: null,
			ghostIndex: null,
			newTitle: '',
			newImage: '',
			newTemplate: 'image-top',
			placeholderImage: 'https://via.placeholder.com/300x200?text=Image',

			// Lifecycle
			init() {
				const saved = localStorage.getItem('cardData');
				if (saved) {
					this.cards = JSON.parse(saved);
				} else {
					this.cards = [
						{ id: 1, title: 'Card One',   image: 'https://via.placeholder.com/300x200?text=Card+1', template: 'image-top' },
						{ id: 2, title: 'Card Two',   image: 'https://via.placeholder.com/300x200?text=Card+2', template: 'image-left' },
						{ id: 3, title: 'Card Three', image: '',                                             template: 'minimal' },
						{ id: 4, title: 'Card Four',  image: '',                                             template: 'badge' }
					];
				}
				// Optional: animate grid reflows (Bulma columns are flex; animations may be subtle)
				if (window.animateCSSGrid && this.$refs.grid) {
					animateCSSGrid.wrap(this.$refs.grid);
				}
			},

			// Derived list with ghost placeholder
			cardsWithGhost() {
				const copy = [...this.cards];
				if (this.draggedIndex !== null && this.ghostIndex !== null) {
					copy.splice(this.ghostIndex, 0, { isGhost: true });
				}
				return copy;
			},

			// DnD
			dragStart(index, event) {
				this.draggedIndex = index;
				event.dataTransfer.effectAllowed = 'move';
				event.dataTransfer.setData('text/plain', index);
				event.dataTransfer.setDragImage(event.target, 0, 0);
			},
			dragOver(event, index) {
				const rect = event.currentTarget.getBoundingClientRect();
				const offsetY = event.clientY - rect.top;
				const midpoint = rect.height / 2;
				this.ghostIndex = offsetY < midpoint ? index : index + 1;
			},
			dragEnd() {
				this.draggedIndex = null;
				this.ghostIndex = null;
			},
			handleDrop() {
				if (this.draggedIndex === null || this.ghostIndex === null) return;
				const moved = this.cards.splice(this.draggedIndex, 1)[0];
				const insertIndex = this.ghostIndex > this.draggedIndex ? this.ghostIndex - 1 : this.ghostIndex;
				this.cards.splice(insertIndex, 0, moved);
				this.saveCards();
				this.dragEnd();
			},

			// CRUD
			addCard() {
				const id = Date.now();
				const title = this.newTitle && this.newTitle.trim() ? this.newTitle.trim() : 'Untitled';
				const image = this.newImage && this.newImage.trim() ? this.newImage.trim() : this.placeholderImage;
				const template = this.newTemplate;

				this.cards.push({ id, title, image, template });
				this.newTitle = '';
				this.newImage = '';
				this.newTemplate = 'image-top';
				this.saveCards();
			},
			removeCard(index) {
				this.cards.splice(index, 1);
				this.saveCards();
			},
			resetCards() {
				this.cards = [];
				localStorage.removeItem('cardData');
			},

			// Persistence
			saveCards() {
				localStorage.setItem('cardData', JSON.stringify(this.cards));
			}
		}
	}
</script>

</body>
</html>
