<?php
/**
 * Simple test script to verify autoloading is working
 */

// Define constants that ImpressCMS expects
define('ICMS_ROOT_PATH', __DIR__ . '/htdocs');
define('ICMS_URL', 'http://localhost');
define('ICMS_THEME_PATH', ICMS_ROOT_PATH . '/themes');
define('ICMS_THEME_URL', ICMS_URL . '/themes');
define('ICMS_LIBRARIES_PATH', ICMS_ROOT_PATH . '/libraries');
define('ICMS_INCLUDE_PATH', ICMS_ROOT_PATH . '/include');

echo "Testing ImpressCMS Composer Autoloading Integration\n";
echo "==================================================\n\n";

// Test 1: Load Composer autoloader
echo "1. Loading Composer autoloader...\n";
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
    echo "   ✓ Composer autoloader loaded successfully\n";
} else {
    echo "   ✗ Composer autoloader not found\n";
    exit(1);
}

// Test 2: Load ImpressCMS core
echo "\n2. Loading ImpressCMS core...\n";
try {
    require_once ICMS_LIBRARIES_PATH . '/icms.php';
    echo "   ✓ ImpressCMS core loaded successfully\n";
} catch (Exception $e) {
    echo "   ✗ Failed to load ImpressCMS core: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 3: Initialize ImpressCMS
echo "\n3. Initializing ImpressCMS...\n";
try {
    icms::setup();
    echo "   ✓ ImpressCMS initialized successfully\n";
} catch (Exception $e) {
    echo "   ✗ Failed to initialize ImpressCMS: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 4: Test class autoloading
echo "\n4. Testing class autoloading...\n";
$testClasses = [
    'icms_core_Security',
    'icms_core_Logger',
    'icms_auth_Factory',
    'icms_config_Handler',
    'icms_module_Object',
    'icms_Autoloader',
    'icms_Event',
    'icms_Utils'
];

$loadedClasses = 0;
foreach ($testClasses as $className) {
    if (class_exists($className, true)) {
        echo "   ✓ {$className} loaded successfully\n";
        $loadedClasses++;
    } else {
        echo "   ✗ Failed to load {$className}\n";
    }
}

echo "\n   Summary: {$loadedClasses}/" . count($testClasses) . " classes loaded successfully\n";

// Test 5: Test bridge functionality
echo "\n5. Testing bridge functionality...\n";
if (class_exists('icms_ComposerAutoloadBridge', true)) {
    echo "   ✓ Bridge class loaded successfully\n";

    if (icms_ComposerAutoloadBridge::isComposerAvailable()) {
        echo "   ✓ Bridge detects Composer availability\n";
    } else {
        echo "   ✗ Bridge does not detect Composer\n";
    }

    $legacyAutoloader = icms_ComposerAutoloadBridge::getLegacyAutoloader();
    if ($legacyAutoloader === 'icms_Autoloader') {
        echo "   ✓ Legacy autoloader properly detected\n";
    } else {
        echo "   ✗ Legacy autoloader not properly detected\n";
    }
} else {
    echo "   ✗ Bridge class not found\n";
}

// Test 6: Test autoloader registration
echo "\n6. Testing autoloader registration...\n";
$autoloaders = spl_autoload_functions();
echo "   Found " . count($autoloaders) . " registered autoloaders:\n";

$bridgeFound = false;
$composerFound = false;
$legacyFound = false;

foreach ($autoloaders as $i => $autoloader) {
    if (is_array($autoloader)) {
        $class = is_object($autoloader[0]) ? get_class($autoloader[0]) : $autoloader[0];
        $method = $autoloader[1];
        echo "   " . ($i + 1) . ". {$class}::{$method}\n";

        if ($class === 'icms_ComposerAutoloadBridge' && $method === 'autoload') {
            $bridgeFound = true;
        } elseif ($class === 'icms_Autoloader' && $method === 'autoload') {
            $legacyFound = true;
        } elseif ($class === 'Composer\\Autoload\\ClassLoader') {
            $composerFound = true;
        }
    } elseif (is_callable($autoloader)) {
        echo "   " . ($i + 1) . ". Callable autoloader\n";
        $composerFound = true;
    }
}

echo "\n   Status:\n";
echo "   " . ($composerFound ? "✓" : "✗") . " Composer autoloader registered\n";
echo "   " . ($bridgeFound ? "✓" : "✗") . " Bridge autoloader registered\n";
echo "   " . ($legacyFound ? "✓" : "✗") . " Legacy autoloader registered\n";

// Test 7: Performance test
echo "\n7. Performance test...\n";
$startTime = microtime(true);

for ($i = 0; $i < 10; $i++) {
    class_exists('icms_core_Security', true);
    class_exists('icms_auth_Factory', true);
    class_exists('icms_config_Handler', true);
}

$endTime = microtime(true);
$duration = ($endTime - $startTime) * 1000; // Convert to milliseconds

echo "   Loaded 30 class instances in " . number_format($duration, 2) . "ms\n";
echo "   Average: " . number_format($duration / 30, 2) . "ms per class\n";

if ($duration < 100) {
    echo "   ✓ Performance is acceptable\n";
} else {
    echo "   ⚠ Performance may need optimization\n";
}

echo "\n==================================================\n";
echo "Autoloading integration test completed successfully!\n";
echo "ImpressCMS is now using Composer's PSR-4 autoloader\n";
echo "with backward compatibility maintained.\n";
